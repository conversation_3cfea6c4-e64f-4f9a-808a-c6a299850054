import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../widgets/app_widgets.dart';
import '../services/security_service.dart';
import '../routes/app_routes.dart';
import '../utils/constants.dart' as constants;
import 'dart:math' as math;

class BehavioralAuthScreen extends StatefulWidget {
  const BehavioralAuthScreen({Key? key}) : super(key: key);

  @override
  State<BehavioralAuthScreen> createState() => _BehavioralAuthScreenState();
}

class _BehavioralAuthScreenState extends State<BehavioralAuthScreen>
    with TickerProviderStateMixin {
  
  // Challenge data
  String _currentChallenge = '';
  int _challengeStep = 0;
  List<String> _challenges = [
    'Swipe in your natural pattern',
    'Type your usual speed',
    'Touch with your normal pressure',
    'Complete the gesture',
  ];
  
  // Behavioral tracking
  List<Map<String, dynamic>> _behavioralData = [];
  DateTime? _startTime;
  List<Offset> _swipePattern = [];
  List<double> _touchPressures = [];
  List<int> _keystrokeTiming = [];
  
  // UI State
  bool _isProcessing = false;
  double _progress = 0.0;
  String _statusMessage = 'Verifying your identity...';
  
  // Animation controllers
  late AnimationController _pulseController;
  late AnimationController _progressController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;
  
  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startBehavioralChallenge();
  }
  
  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
  }
  
  void _startBehavioralChallenge() {
    setState(() {
      _currentChallenge = _challenges[_challengeStep];
      _startTime = DateTime.now();
    });
  }
  
  void _onPanStart(DragStartDetails details) {
    _swipePattern.clear();
    _swipePattern.add(details.localPosition);
  }
  
  void _onPanUpdate(DragUpdateDetails details) {
    _swipePattern.add(details.localPosition);
    
    // Simulate pressure (in real app, use details.pressure if available)
    final pressure = 0.5 + (math.Random().nextDouble() * 0.5);
    _touchPressures.add(pressure);
  }
  
  void _onPanEnd(DragEndDetails details) {
    _recordSwipeData();
    _nextChallenge();
  }
  
  void _recordSwipeData() {
    if (_swipePattern.length < 2) return;
    
    // Calculate swipe metrics
    final distance = _calculateSwipeDistance();
    final velocity = _calculateSwipeVelocity();
    final duration = DateTime.now().difference(_startTime!).inMilliseconds;
    
    final data = {
      'type': 'swipe',
      'distance': distance,
      'velocity': velocity,
      'duration': duration,
      'pattern_points': _swipePattern.length,
      'avg_pressure': _touchPressures.isNotEmpty 
          ? _touchPressures.reduce((a, b) => a + b) / _touchPressures.length 
          : 0.5,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    
    _behavioralData.add(data);
  }
  
  double _calculateSwipeDistance() {
    if (_swipePattern.length < 2) return 0.0;
    
    double totalDistance = 0.0;
    for (int i = 1; i < _swipePattern.length; i++) {
      final dx = _swipePattern[i].dx - _swipePattern[i - 1].dx;
      final dy = _swipePattern[i].dy - _swipePattern[i - 1].dy;
      totalDistance += math.sqrt(dx * dx + dy * dy);
    }
    return totalDistance;
  }
  
  double _calculateSwipeVelocity() {
    if (_swipePattern.length < 2 || _startTime == null) return 0.0;
    
    final distance = _calculateSwipeDistance();
    final duration = DateTime.now().difference(_startTime!).inMilliseconds;
    return distance / (duration / 1000); // pixels per second
  }
  
  void _nextChallenge() {
    setState(() {
      _progress = (_challengeStep + 1) / _challenges.length;
    });
    
    _progressController.animateTo(_progress);
    
    if (_challengeStep < _challenges.length - 1) {
      _challengeStep++;
      _startBehavioralChallenge();
    } else {
      _completeBehavioralAuth();
    }
  }
  
  Future<void> _completeBehavioralAuth() async {
    setState(() {
      _isProcessing = true;
      _statusMessage = 'Analyzing behavioral patterns...';
    });
    
    try {
      // Record all behavioral data
      for (final data in _behavioralData) {
        await SecurityService.recordBehavioralData(data);
      }
      
      // Verify behavioral pattern
      final isValid = await SecurityService.verifyBehavioralPattern({
        'swipe_velocity': _behavioralData.isNotEmpty ? _behavioralData.last['velocity'] : 0.0,
        'swipe_pressure': _behavioralData.isNotEmpty ? _behavioralData.last['avg_pressure'] : 0.5,
      });
      
      await Future.delayed(const Duration(milliseconds: 1500));
      
      if (isValid) {
        setState(() => _statusMessage = 'Authentication successful!');
        await Future.delayed(const Duration(milliseconds: 800));
        Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
      } else {
        setState(() => _statusMessage = 'Authentication failed. Please try again.');
        await Future.delayed(const Duration(milliseconds: 2000));
        _retryAuthentication();
      }
      
    } catch (e) {
      setState(() => _statusMessage = 'Authentication error. Please try again.');
      await Future.delayed(const Duration(milliseconds: 2000));
      _retryAuthentication();
    }
  }
  
  void _retryAuthentication() {
    setState(() {
      _challengeStep = 0;
      _behavioralData.clear();
      _progress = 0.0;
      _isProcessing = false;
    });
    _progressController.reset();
    _startBehavioralChallenge();
  }
  
  @override
  void dispose() {
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.background,
              AppColors.surface,
              AppColors.background,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Header
                _buildHeader(),
                
                Expanded(
                  child: _isProcessing 
                      ? _buildProcessingView()
                      : _buildChallengeView(),
                ),
                
                // Footer
                _buildFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    return Column(
      children: [
        const SizedBox(height: 20),
        
        // Security Icon
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [AppColors.primary, AppColors.primaryDark],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.fingerprint,
                  size: 40,
                  color: Colors.white,
                ),
              ),
            );
          },
        ),
        const SizedBox(height: 24),
        
        // Title
        Text(
          'Behavioral Authentication',
          style: AppTextStyles.heading2.copyWith(
            fontWeight: FontWeight.w800,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        
        // Subtitle
        Text(
          'We need to verify your identity using behavioral patterns',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        
        // Progress Bar
        _buildProgressBar(),
      ],
    );
  }
  
  Widget _buildProgressBar() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Step ${_challengeStep + 1} of ${_challenges.length}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              '${(_progress * 100).toInt()}%',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            return LinearProgressIndicator(
              value: _progressAnimation.value,
              backgroundColor: AppColors.surfaceVariant,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              borderRadius: BorderRadius.circular(4),
            );
          },
        ),
      ],
    );
  }
  
  Widget _buildChallengeView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Challenge Instruction
        AppCard(
          backgroundColor: AppColors.primary.withOpacity(0.1),
          child: Column(
            children: [
              Icon(
                Icons.gesture,
                color: AppColors.primary,
                size: 32,
              ),
              const SizedBox(height: 16),
              Text(
                _currentChallenge,
                style: AppTextStyles.heading4.copyWith(
                  color: AppColors.primary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 40),
        
        // Gesture Area
        GestureDetector(
          onPanStart: _onPanStart,
          onPanUpdate: _onPanUpdate,
          onPanEnd: _onPanEnd,
          child: Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.primary.withOpacity(0.3),
                width: 2,
              ),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.touch_app,
                    color: AppColors.textSecondary,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Swipe here in your natural pattern',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildProcessingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 60,
            height: 60,
            child: CircularProgressIndicator(
              strokeWidth: 4,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          ),
          const SizedBox(height: 24),
          
          Text(
            _statusMessage,
            style: AppTextStyles.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          
          Text(
            'This may take a few seconds...',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildFooter() {
    return Column(
      children: [
        // Security Notice
        AppCard(
          backgroundColor: AppColors.surfaceVariant,
          child: Row(
            children: [
              Icon(
                Icons.security,
                color: AppColors.textSecondary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Your behavioral patterns are analyzed locally and encrypted for security.',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // Skip Option (for demo)
        TextButton(
          onPressed: () {
            Navigator.pushReplacementNamed(context, AppRoutes.dashboard);
          },
          child: Text(
            'Skip for now (Demo)',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
        ),
      ],
    );
  }
}
