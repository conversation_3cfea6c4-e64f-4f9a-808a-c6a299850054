import 'package:intl/intl.dart';

class CurrencyFormatter {
  static final NumberFormat _inrFormatter = NumberFormat.currency(
    locale: 'en_IN',
    symbol: '₹',
    decimalDigits: 2,
  );

  static final NumberFormat _inrFormatterNoSymbol = NumberFormat.currency(
    locale: 'en_IN',
    symbol: '',
    decimalDigits: 2,
  );

  static final NumberFormat _compactFormatter = NumberFormat.compactCurrency(
    locale: 'en_IN',
    symbol: '₹',
    decimalDigits: 2,
  );

  /// Format amount to INR with symbol (₹1,23,456.78)
  static String formatINR(double amount) {
    return _inrFormatter.format(amount);
  }

  /// Format amount to INR without symbol (1,23,456.78)
  static String formatINRNoSymbol(double amount) {
    return _inrFormatterNoSymbol.format(amount).trim();
  }

  /// Format amount to compact INR (₹1.23L, ₹12.3K)
  static String formatINRCompact(double amount) {
    return _compactFormatter.format(amount);
  }

  /// Format amount with custom symbol
  static String formatWithSymbol(double amount, String symbol) {
    return '$symbol${formatINRNoSymbol(amount)}';
  }

  /// Parse INR string to double
  static double parseINR(String inrString) {
    try {
      // Remove currency symbol and spaces
      String cleanString = inrString
          .replaceAll('₹', '')
          .replaceAll(',', '')
          .replaceAll(' ', '')
          .trim();
      return double.parse(cleanString);
    } catch (e) {
      return 0.0;
    }
  }

  /// Format transaction amount with + or - prefix
  static String formatTransactionAmount(double amount, {bool isCredit = false}) {
    String prefix = isCredit ? '+' : '-';
    return '$prefix${formatINR(amount.abs())}';
  }

  /// Get currency symbol
  static String get currencySymbol => '₹';

  /// Format large amounts in Indian numbering system
  static String formatIndianNumbering(double amount) {
    if (amount >= ********) {
      // Crores
      return '${formatINR(amount / ********)} Cr';
    } else if (amount >= 100000) {
      // Lakhs
      return '${formatINR(amount / 100000)} L';
    } else if (amount >= 1000) {
      // Thousands
      return '${formatINR(amount / 1000)} K';
    } else {
      return formatINR(amount);
    }
  }

  /// Format percentage
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// Format account number (mask middle digits)
  static String formatAccountNumber(String accountNumber) {
    if (accountNumber.length <= 4) return accountNumber;
    
    String first = accountNumber.substring(0, 2);
    String last = accountNumber.substring(accountNumber.length - 2);
    String middle = '*' * (accountNumber.length - 4);
    
    return '$first$middle$last';
  }

  /// Format card number (mask middle digits)
  static String formatCardNumber(String cardNumber) {
    if (cardNumber.length != 16) return cardNumber;
    
    return '${cardNumber.substring(0, 4)} **** **** ${cardNumber.substring(12)}';
  }
}

/// Extension for easy currency formatting
extension CurrencyExtension on double {
  String get inr => CurrencyFormatter.formatINR(this);
  String get inrCompact => CurrencyFormatter.formatINRCompact(this);
  String get inrNoSymbol => CurrencyFormatter.formatINRNoSymbol(this);
  String get percentage => CurrencyFormatter.formatPercentage(this);
}

extension StringCurrencyExtension on String {
  double get parseINR => CurrencyFormatter.parseINR(this);
}
