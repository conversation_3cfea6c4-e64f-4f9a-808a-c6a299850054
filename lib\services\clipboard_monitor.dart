import 'package:flutter/services.dart';

class ClipboardMonitor {
  static Future<bool> checkPasteAttempt(String currentText) async {
    try {
      final data = await Clipboard.getData('text/plain');
      if (data != null && data.text != null) {
        // If clipboard has text different from current input, assume paste
        return data.text != currentText;
      }
    } catch (_) {
      // Fail silently
    }
    return false;
  }
}
