class DemoData {
  static const String userName = "<PERSON><PERSON><PERSON>";
  static const double balance = 7024.00;
  static const String cardNumber = "2004 2001 0611 7177";
  
  static final List<Map<String, dynamic>> quickSendContacts = [
    {
      'name': '<PERSON><PERSON>',
      'imageUrl': null,
    },
    {
      'name': '<PERSON><PERSON><PERSON>',
      'imageUrl': null,
    },
    {
      'name': '<PERSON><PERSON><PERSON>',
      'imageUrl': null,
    },
    {
      'name': '<PERSON>ay<PERSON>',
      'imageUrl': null,
    },
  ];
  
  static final List<Map<String, dynamic>> recentTransactions = [
    {
      'title': '<PERSON><PERSON><PERSON>',
      'subtitle': 'Receive money',
      'amount': '12.00',
      'isIncoming': true,
      'icon': null,
    },
    {
      'title': 'Pertiwi Adi',
      'subtitle': 'Send money',
      'amount': '12.00',
      'isIncoming': false,
      'icon': null,
    },
  ];
  
  static final List<Map<String, dynamic>> weeklySpendingData = [
    {'day': 'Jan', 'earned': 30, 'spent': 25},
    {'day': 'Feb', 'earned': 35, 'spent': 30},
    {'day': 'Apr', 'earned': 75, 'spent': 65},
    {'day': 'Mar', 'earned': 45, 'spent': 40},
    {'day': 'May', 'earned': 65, 'spent': 55},
    {'day': 'Jun', 'earned': 80, 'spent': 70},
  ];
  
  static const double totalEarned = 210.00;
  static const double totalSpent = 210.00;
  
  static final List<Map<String, dynamic>> savingGoals = [
    {
      'title': 'Rubicon Car',
      'subtitle': '30% of \$52,728.72',
      'subtext': 'GK Group 4X4',
      'amount': 182.00,
      'date': 'Today',
    },
    {
      'title': 'Macbook Pro 2022',
      'subtitle': '30% of \$1,728.72',
      'subtext': 'iBox Indonesia',
      'amount': 59.00,
      'date': 'March 24, 2023',
    },
  ];
  
  // Payment Success Data
  static const String paymentAmount = "12,728.72";
  static const String savedAmount = "59.00";
  static const String referenceNumber = "123190310923122";
  static const String paymentCode = "PAY-001822";
  static const String merchantName = "iBox Indonesia";
  static const String paymentDate = "March 23, 2023";
  static const String paymentTime = "08:30 PM";
  static const String sender = "Dhimas Mohammed";
  static const String discount = "59.00";
}
