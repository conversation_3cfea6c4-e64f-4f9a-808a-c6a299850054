<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Banking app color scheme -->
    <color name="banking_primary">#667eea</color>
    <color name="banking_primary_dark">#16213e</color>
    <color name="banking_accent">#4facfe</color>
    <color name="banking_background">#1a1a2e</color>
    
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             the Flutter engine draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:statusBarColor">@color/banking_primary_dark</item>
        <item name="android:navigationBarColor">@color/banking_primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>
    
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">@color/banking_background</item>
        <item name="android:statusBarColor">@color/banking_primary_dark</item>
        <item name="android:navigationBarColor">@color/banking_primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        <!-- Banking security: Prevent screenshots -->
        <item name="android:windowDisablePreview">true</item>
    </style>
</resources>
