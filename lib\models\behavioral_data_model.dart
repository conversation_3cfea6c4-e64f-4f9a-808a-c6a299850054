import 'dart:ui';

class BehavioralData {
  List<int> keystrokeTimes = [];
  List<double> tapPressures = [];
  List<Offset> tapPositions = [];
  List<double> swipeVelocities = [];
  List<int> tapTimestamps = [];
  List<int> swipeDurations = [];
  int sessionStartTime = DateTime.now().millisecondsSinceEpoch;
  String usageTimeOfDay = '';

  // Computed property for keystroke timings compatibility
  List<int> get keystrokeTimings => _calculateIntervals(keystrokeTimes);

  void addKeystroke(int timestamp) {
    keystrokeTimes.add(timestamp);
  }

  void addTapData(double pressure, Offset position) {
    tapPressures.add(pressure);
    tapPositions.add(position);
    tapTimestamps.add(DateTime.now().millisecondsSinceEpoch);
  }

  void addSwipeVelocity(double velocity) {
    swipeVelocities.add(velocity);
  }

  void addSwipeDuration(int duration) {
    swipeDurations.add(duration);
  }

  double get typingSpeedKPM {
    final durationMinutes =
        (DateTime.now().millisecondsSinceEpoch - sessionStartTime) / 60000;
    if (durationMinutes <= 0) return 0;
    return keystrokeTimes.length / durationMinutes;
  }

  Map<String, dynamic> toJson() {
    return {
      'keystroke_intervals': _calculateIntervals(keystrokeTimes),
      'avg_tap_pressure': tapPressures.isEmpty
          ? 0
          : tapPressures.reduce((a, b) => a + b) / tapPressures.length,
      'tap_positions':
          tapPositions.map((pos) => {'x': pos.dx, 'y': pos.dy}).toList(),
      'avg_swipe_velocity': swipeVelocities.isEmpty
          ? 0
          : swipeVelocities.reduce((a, b) => a + b) / swipeVelocities.length,
      'session_duration':
          DateTime.now().millisecondsSinceEpoch - sessionStartTime,
      'tap_timestamps': tapTimestamps,
      'swipe_durations': swipeDurations,
      'typing_speed_kpm': typingSpeedKPM,
      'usage_time_of_day': usageTimeOfDay,
    };
  }

  List<int> _calculateIntervals(List<int> times) {
    if (times.length < 2) return [];
    List<int> intervals = [];
    for (int i = 1; i < times.length; i++) {
      intervals.add(times[i] - times[i - 1]);
    }
    return intervals;
  }
}
