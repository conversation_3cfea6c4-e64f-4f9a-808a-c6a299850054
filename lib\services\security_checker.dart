import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';

class SecurityChecker {
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  /// Comprehensive security check for banking app
  static Future<SecurityResult> performSecurityCheck() async {
    final List<SecurityThreat> threats = [];

    // Check for emulator
    if (await _isEmulator()) {
      threats.add(SecurityThreat(
        type: ThreatType.emulator,
        severity: ThreatSeverity.high,
        description: 'Running on emulator/simulator',
      ));
    }

    // Check for root/jailbreak
    if (await _isRootedOrJailbroken()) {
      threats.add(SecurityThreat(
        type: ThreatType.rootJailbreak,
        severity: ThreatSeverity.critical,
        description: 'Device is rooted or jailbroken',
      ));
    }

    // Check for debugging
    if (await _isDebugging()) {
      threats.add(SecurityThreat(
        type: ThreatType.debugging,
        severity: ThreatSeverity.medium,
        description: 'App is running in debug mode',
      ));
    }

    // Check for suspicious apps (basic check)
    if (await _hasSuspiciousApps()) {
      threats.add(SecurityThreat(
        type: ThreatType.suspiciousApps,
        severity: ThreatSeverity.medium,
        description: 'Suspicious security apps detected',
      ));
    }

    return SecurityResult(
      isSecure: threats.isEmpty || _areThreatsAcceptable(threats),
      threats: threats,
      riskLevel: _calculateRiskLevel(threats),
    );
  }

  static Future<bool> _isEmulator() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        
        // Common emulator indicators
        final model = androidInfo.model.toLowerCase();
        final product = androidInfo.product.toLowerCase();
        final hardware = androidInfo.hardware.toLowerCase();
        final fingerprint = androidInfo.fingerprint.toLowerCase();

        // Check for emulator signatures
        return model.contains('emulator') ||
               model.contains('android sdk') ||
               product.contains('sdk') ||
               product.contains('emulator') ||
               hardware.contains('goldfish') ||
               hardware.contains('ranchu') ||
               fingerprint.contains('generic') ||
               fingerprint.contains('emulator') ||
               androidInfo.isPhysicalDevice == false;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return iosInfo.isPhysicalDevice == false;
      }
    } catch (e) {
      // If we can't determine, assume it's not an emulator
      return false;
    }
    return false;
  }

  static Future<bool> _isRootedOrJailbroken() async {
    try {
      if (Platform.isAndroid) {
        return await _isAndroidRooted();
      } else if (Platform.isIOS) {
        return await _isIOSJailbroken();
      }
    } catch (e) {
      return false;
    }
    return false;
  }

  static Future<bool> _isAndroidRooted() async {
    // Common root detection methods
    final List<String> rootPaths = [
      '/system/app/Superuser.apk',
      '/sbin/su',
      '/system/bin/su',
      '/system/xbin/su',
      '/data/local/xbin/su',
      '/data/local/bin/su',
      '/system/sd/xbin/su',
      '/system/bin/failsafe/su',
      '/data/local/su',
      '/su/bin/su',
    ];

    for (String path in rootPaths) {
      if (await File(path).exists()) {
        return true;
      }
    }

    // Check for common root management apps
    final List<String> rootApps = [
      '/system/app/SuperSU',
      '/system/app/Kinguser',
      '/system/app/Magisk',
    ];

    for (String app in rootApps) {
      if (await Directory(app).exists()) {
        return true;
      }
    }

    return false;
  }

  static Future<bool> _isIOSJailbroken() async {
    // Common jailbreak detection paths
    final List<String> jailbreakPaths = [
      '/Applications/Cydia.app',
      '/Library/MobileSubstrate/MobileSubstrate.dylib',
      '/bin/bash',
      '/usr/sbin/sshd',
      '/etc/apt',
      '/private/var/lib/apt',
      '/Applications/blackra1n.app',
      '/Applications/FakeCarrier.app',
      '/Applications/Icy.app',
      '/Applications/IntelliScreen.app',
      '/Applications/MxTube.app',
      '/Applications/RockApp.app',
      '/Applications/SBSettings.app',
      '/Applications/WinterBoard.app',
    ];

    for (String path in jailbreakPaths) {
      if (await File(path).exists() || await Directory(path).exists()) {
        return true;
      }
    }

    return false;
  }

  static Future<bool> _isDebugging() async {
    // In release mode, this should always return false
    return kDebugMode;
  }

  static Future<bool> _hasSuspiciousApps() async {
    // This is a simplified check - in a real implementation,
    // you might want to check for specific security testing apps
    try {
      if (Platform.isAndroid) {
        // Check for common security testing tools
        final suspiciousPackages = [
          '/system/app/Xposed',
          '/system/app/EdXposed',
          '/system/app/LSPosed',
        ];

        for (String pkg in suspiciousPackages) {
          if (await Directory(pkg).exists()) {
            return true;
          }
        }
      }
    } catch (e) {
      return false;
    }
    return false;
  }

  static bool _areThreatsAcceptable(List<SecurityThreat> threats) {
    // Only allow low severity threats in production
    return threats.every((threat) => threat.severity == ThreatSeverity.low);
  }

  static RiskLevel _calculateRiskLevel(List<SecurityThreat> threats) {
    if (threats.isEmpty) return RiskLevel.none;

    final hasCritical = threats.any((t) => t.severity == ThreatSeverity.critical);
    final hasHigh = threats.any((t) => t.severity == ThreatSeverity.high);
    final hasMedium = threats.any((t) => t.severity == ThreatSeverity.medium);

    if (hasCritical) return RiskLevel.critical;
    if (hasHigh) return RiskLevel.high;
    if (hasMedium) return RiskLevel.medium;
    return RiskLevel.low;
  }
}

class SecurityResult {
  final bool isSecure;
  final List<SecurityThreat> threats;
  final RiskLevel riskLevel;

  SecurityResult({
    required this.isSecure,
    required this.threats,
    required this.riskLevel,
  });

  String get riskDescription {
    switch (riskLevel) {
      case RiskLevel.none:
        return 'Device is secure';
      case RiskLevel.low:
        return 'Low security risk detected';
      case RiskLevel.medium:
        return 'Medium security risk detected';
      case RiskLevel.high:
        return 'High security risk detected';
      case RiskLevel.critical:
        return 'Critical security risk detected';
    }
  }
}

class SecurityThreat {
  final ThreatType type;
  final ThreatSeverity severity;
  final String description;

  SecurityThreat({
    required this.type,
    required this.severity,
    required this.description,
  });
}

enum ThreatType {
  emulator,
  rootJailbreak,
  debugging,
  suspiciousApps,
}

enum ThreatSeverity {
  low,
  medium,
  high,
  critical,
}

enum RiskLevel {
  none,
  low,
  medium,
  high,
  critical,
}
