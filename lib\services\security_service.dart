import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:math' as math;

class SecurityService {
  static const String _keyDeviceFingerprint = 'device_fingerprint';
  static const String _keySecurityLevel = 'security_level';
  static const String _keyTrustScore = 'trust_score';
  static const String _keyBehavioralProfile = 'behavioral_profile';
  
  static SharedPreferences? _prefs;
  static DeviceInfoPlugin? _deviceInfo;

  static Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
    _deviceInfo ??= DeviceInfoPlugin();
    
    // Generate device fingerprint on first run
    if (!_prefs!.containsKey(_keyDeviceFingerprint)) {
      await _generateDeviceFingerprint();
    }
  }

  static Future<bool> checkDeviceSecurity() async {
    try {
      // Check if device is rooted/jailbroken
      final isRooted = await _checkRootStatus();
      if (isRooted) {
        return false;
      }

      // Check if debugging is enabled
      final isDebugging = await _checkDebuggingStatus();
      if (isDebugging && !kDebugMode) {
        return false;
      }

      // Check device fingerprint consistency
      final fingerprintValid = await _verifyDeviceFingerprint();
      if (!fingerprintValid) {
        return false;
      }

      await _updateSecurityLevel('high');
      return true;
    } catch (e) {
      await _updateSecurityLevel('low');
      return false;
    }
  }

  static Future<void> initializeBiometrics() async {
    try {
      // In a real app, you would check for biometric availability
      // For demo purposes, we'll simulate this
      await Future.delayed(const Duration(milliseconds: 500));
      
      final hasBiometrics = await _checkBiometricAvailability();
      if (hasBiometrics) {
        await _prefs!.setBool('biometrics_available', true);
      }
    } catch (e) {
      // Biometric initialization failed - continue without biometrics
    }
  }

  static Future<void> initializeBehavioralAuth() async {
    try {
      // Initialize behavioral authentication patterns
      final profile = await _loadBehavioralProfile();
      if (profile == null) {
        await _createInitialBehavioralProfile();
      }
    } catch (e) {
      // Behavioral auth initialization failed - continue with reduced security
    }
  }

  static Future<double> calculateTrustScore() async {
    await initialize();
    
    double score = 0.0;
    
    // Device security (30%)
    final securityLevel = _prefs!.getString(_keySecurityLevel) ?? 'low';
    switch (securityLevel) {
      case 'high':
        score += 30.0;
        break;
      case 'medium':
        score += 20.0;
        break;
      case 'low':
        score += 10.0;
        break;
    }
    
    // Behavioral consistency (40%)
    final behavioralScore = await _calculateBehavioralScore();
    score += behavioralScore * 0.4;
    
    // Device consistency (20%)
    final deviceScore = await _calculateDeviceScore();
    score += deviceScore * 0.2;
    
    // Time-based factors (10%)
    final timeScore = _calculateTimeBasedScore();
    score += timeScore * 0.1;
    
    // Store updated trust score
    await _prefs!.setDouble(_keyTrustScore, score);
    
    return score.clamp(0.0, 100.0);
  }

  static Future<Map<String, dynamic>> getBehavioralMetrics() async {
    await initialize();
    
    final profile = await _loadBehavioralProfile();
    if (profile == null) {
      return {};
    }
    
    return {
      'typing_pattern': profile['typing_pattern'] ?? {},
      'swipe_patterns': profile['swipe_patterns'] ?? {},
      'touch_pressure': profile['touch_pressure'] ?? {},
      'app_usage_time': profile['app_usage_time'] ?? {},
      'login_frequency': profile['login_frequency'] ?? {},
    };
  }

  static Future<void> recordBehavioralData(Map<String, dynamic> data) async {
    await initialize();
    
    try {
      final profile = await _loadBehavioralProfile() ?? {};
      
      // Update profile with new data
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      data['timestamp'] = timestamp;
      
      // Merge with existing profile
      for (final key in data.keys) {
        if (key != 'timestamp') {
          profile[key] = profile[key] ?? [];
          (profile[key] as List).add(data[key]);
          
          // Keep only last 50 entries per metric
          if ((profile[key] as List).length > 50) {
            (profile[key] as List).removeAt(0);
          }
        }
      }
      
      await _saveBehavioralProfile(profile);
    } catch (e) {
      // Error recording behavioral data - continue without recording
    }
  }

  static Future<bool> verifyBehavioralPattern(Map<String, dynamic> currentData) async {
    await initialize();
    
    try {
      final profile = await _loadBehavioralProfile();
      if (profile == null) {
        return true; // Allow on first use
      }
      
      double matchScore = 0.0;
      int metrics = 0;
      
      // Check typing pattern
      if (currentData.containsKey('typing_speed') && profile.containsKey('typing_pattern')) {
        final currentSpeed = currentData['typing_speed'] as double;
        final historicalSpeeds = (profile['typing_pattern'] as List).cast<double>();
        final avgSpeed = historicalSpeeds.reduce((a, b) => a + b) / historicalSpeeds.length;
        final deviation = (currentSpeed - avgSpeed).abs() / avgSpeed;
        matchScore += (1.0 - deviation.clamp(0.0, 1.0));
        metrics++;
      }
      
      // Check swipe patterns
      if (currentData.containsKey('swipe_velocity') && profile.containsKey('swipe_patterns')) {
        final currentVelocity = currentData['swipe_velocity'] as double;
        final historicalVelocities = (profile['swipe_patterns'] as List).cast<double>();
        final avgVelocity = historicalVelocities.reduce((a, b) => a + b) / historicalVelocities.length;
        final deviation = (currentVelocity - avgVelocity).abs() / avgVelocity;
        matchScore += (1.0 - deviation.clamp(0.0, 1.0));
        metrics++;
      }
      
      if (metrics == 0) return true;
      
      final finalScore = matchScore / metrics;
      return finalScore > 0.6; // 60% similarity threshold

    } catch (e) {
      return true; // Allow on error to prevent lockout
    }
  }

  // Private methods
  static Future<void> _generateDeviceFingerprint() async {
    try {
      Map<String, dynamic> fingerprint = {};
      
      if (defaultTargetPlatform == TargetPlatform.android) {
        final androidInfo = await _deviceInfo!.androidInfo;
        fingerprint = {
          'platform': 'android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'version': androidInfo.version.release,
          'sdk': androidInfo.version.sdkInt,
          'board': androidInfo.board,
          'bootloader': androidInfo.bootloader,
          'device': androidInfo.device,
          'display': androidInfo.display,
          'fingerprint': androidInfo.fingerprint,
          'hardware': androidInfo.hardware,
          'host': androidInfo.host,
          'id': androidInfo.id,
          'product': androidInfo.product,
        };
      } else if (defaultTargetPlatform == TargetPlatform.iOS) {
        final iosInfo = await _deviceInfo!.iosInfo;
        fingerprint = {
          'platform': 'ios',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'system_name': iosInfo.systemName,
          'system_version': iosInfo.systemVersion,
          'identifier': iosInfo.identifierForVendor,
        };
      }
      
      await _prefs!.setString(_keyDeviceFingerprint, jsonEncode(fingerprint));
    } catch (e) {
      // Error generating device fingerprint - continue without fingerprint
    }
  }

  static Future<bool> _checkRootStatus() async {
    try {
      if (Platform.isAndroid) {
        // Check for common root indicators
        final commonRootPaths = [
          '/system/app/Superuser.apk',
          '/sbin/su',
          '/system/bin/su',
          '/system/xbin/su',
          '/data/local/xbin/su',
          '/data/local/bin/su',
          '/system/sd/xbin/su',
          '/system/bin/failsafe/su',
          '/data/local/su',
          '/su/bin/su',
        ];

        for (String path in commonRootPaths) {
          if (await File(path).exists()) {
            return true;
          }
        }

        // Check for root management apps
        final rootApps = [
          '/system/app/SuperSU',
          '/system/app/Kinguser',
          '/system/app/Magisk',
        ];

        for (String app in rootApps) {
          if (await Directory(app).exists()) {
            return true;
          }
        }
      } else if (Platform.isIOS) {
        // Check for jailbreak indicators
        final jailbreakPaths = [
          '/Applications/Cydia.app',
          '/Library/MobileSubstrate/MobileSubstrate.dylib',
          '/bin/bash',
          '/usr/sbin/sshd',
          '/etc/apt',
          '/private/var/lib/apt/',
        ];

        for (String path in jailbreakPaths) {
          if (await File(path).exists() || await Directory(path).exists()) {
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      // If we can't check, assume not rooted for user experience
      return false;
    }
  }

  static Future<bool> _checkDebuggingStatus() async {
    return kDebugMode;
  }

  static Future<bool> _verifyDeviceFingerprint() async {
    try {
      final storedFingerprint = _prefs!.getString(_keyDeviceFingerprint);
      if (storedFingerprint == null) return false;
      
      // In a real app, you'd compare with current device info
      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> _checkBiometricAvailability() async {
    // Simulate biometric availability check
    return true;
  }

  static Future<void> _updateSecurityLevel(String level) async {
    await _prefs!.setString(_keySecurityLevel, level);
  }

  static Future<Map<String, dynamic>?> _loadBehavioralProfile() async {
    final profileJson = _prefs!.getString(_keyBehavioralProfile);
    if (profileJson != null) {
      return jsonDecode(profileJson);
    }
    return null;
  }

  static Future<void> _saveBehavioralProfile(Map<String, dynamic> profile) async {
    await _prefs!.setString(_keyBehavioralProfile, jsonEncode(profile));
  }

  static Future<void> _createInitialBehavioralProfile() async {
    final initialProfile = {
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'typing_pattern': <double>[],
      'swipe_patterns': <double>[],
      'touch_pressure': <double>[],
      'app_usage_time': <int>[],
      'login_frequency': <int>[],
    };
    
    await _saveBehavioralProfile(initialProfile);
  }

  static Future<double> _calculateBehavioralScore() async {
    final profile = await _loadBehavioralProfile();
    if (profile == null) return 0.0;
    
    // Calculate consistency score based on behavioral data
    double consistency = 0.0;
    int metrics = 0;
    
    for (final key in ['typing_pattern', 'swipe_patterns', 'touch_pressure']) {
      if (profile.containsKey(key) && (profile[key] as List).isNotEmpty) {
        final data = (profile[key] as List).cast<double>();
        final variance = _calculateVariance(data);
        consistency += (1.0 - variance.clamp(0.0, 1.0));
        metrics++;
      }
    }
    
    return metrics > 0 ? (consistency / metrics) * 100 : 0.0;
  }

  static Future<double> _calculateDeviceScore() async {
    // Calculate device consistency score
    return 85.0; // Simplified for demo
  }

  static double _calculateTimeBasedScore() {
    final hour = DateTime.now().hour;
    // Higher score during typical banking hours (9 AM - 6 PM)
    if (hour >= 9 && hour <= 18) {
      return 100.0;
    } else if (hour >= 6 && hour <= 22) {
      return 70.0;
    } else {
      return 30.0; // Lower score for unusual hours
    }
  }

  static double _calculateVariance(List<double> data) {
    if (data.isEmpty) return 0.0;
    
    final mean = data.reduce((a, b) => a + b) / data.length;
    final variance = data.map((x) => pow(x - mean, 2)).reduce((a, b) => a + b) / data.length;
    return sqrt(variance) / mean; // Coefficient of variation
  }
}

double pow(double base, int exponent) {
  return math.pow(base, exponent).toDouble();
}

double sqrt(double value) {
  return math.sqrt(value);
}
