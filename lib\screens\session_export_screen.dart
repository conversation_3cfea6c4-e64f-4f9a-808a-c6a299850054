import 'package:flutter/material.dart';
import '../services/data_exporter.dart';
import '../models/behavioral_data_model.dart';

class SessionExportScreen extends StatefulWidget {
  final String userId;
  final BehavioralData? currentSession;

  const SessionExportScreen({
    required this.userId,
    this.currentSession,
    Key? key,
  }) : super(key: key);

  @override
  State<SessionExportScreen> createState() => _SessionExportScreenState();
}

class _SessionExportScreenState extends State<SessionExportScreen> {
  final DataExporter _exporter = DataExporter();
  
  String _selectedFormat = 'json';
  String _selectedExportType = 'current';
  DateTime _startDate = DateTime.now().subtract(Duration(days: 7));
  DateTime _endDate = DateTime.now();
  bool _isExporting = false;
  Map<String, dynamic>? _exportStats;

  @override
  void initState() {
    super.initState();
    _loadExportStats();
  }

  Future<void> _loadExportStats() async {
    final stats = await _exporter.getExportStats(widget.userId);
    setState(() => _exportStats = stats);
  }

  Future<void> _performExport() async {
    setState(() => _isExporting = true);
    
    try {
      switch (_selectedExportType) {
        case 'current':
          if (widget.currentSession != null) {
            await _exporter.exportCurrentSession(widget.userId, widget.currentSession!);
            _showSuccessSnackBar('Current session exported successfully!');
          } else {
            _showErrorSnackBar('No current session data available');
          }
          break;
          
        case 'all':
          await _exporter.exportUserSessions(widget.userId, format: _selectedFormat);
          _showSuccessSnackBar('All sessions exported successfully!');
          break;
          
        case 'date_range':
          await _exporter.exportSessionsByDateRange(
            widget.userId,
            _startDate,
            _endDate,
            format: _selectedFormat,
          );
          _showSuccessSnackBar('Sessions exported successfully for selected date range!');
          break;
      }
      
      await _loadExportStats(); // Refresh stats
    } catch (e) {
      _showErrorSnackBar('Export failed: ${e.toString()}');
    } finally {
      setState(() => _isExporting = false);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFF1a1a2e),
      appBar: AppBar(
        title: Text('Export Sessions', style: TextStyle(color: Colors.white)),
        backgroundColor: Color(0xFF16213e),
        iconTheme: IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF1a1a2e), Color(0xFF16213e)],
          ),
        ),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildExportStatsCard(),
              SizedBox(height: 20),
              _buildExportTypeCard(),
              SizedBox(height: 20),
              _buildFormatSelectionCard(),
              if (_selectedExportType == 'date_range') ...[
                SizedBox(height: 20),
                _buildDateRangeCard(),
              ],
              SizedBox(height: 30),
              _buildExportButton(),
              SizedBox(height: 20),
              _buildExportGuideCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExportStatsCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: Colors.cyan, size: 24),
              SizedBox(width: 12),
              Text(
                'Export Statistics',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          if (_exportStats != null) ...[
            _buildStatRow('Total Sessions', '${_exportStats!['totalSessions']}', Icons.analytics),
            _buildStatRow('Exported Files', '${_exportStats!['exportedFiles']}', Icons.file_download),
            _buildStatRow('Export Location', _getShortPath(_exportStats!['lastExportPath']), Icons.folder),
          ] else
            Center(child: CircularProgressIndicator(color: Colors.cyan)),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value, IconData icon) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.white70, size: 20),
          SizedBox(width: 12),
          Expanded(
            child: Text(label, style: TextStyle(color: Colors.white70, fontSize: 14)),
          ),
          Text(value, style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.w500)),
        ],
      ),
    );
  }

  String _getShortPath(String path) {
    if (path.length > 30) {
      return '...${path.substring(path.length - 27)}';
    }
    return path;
  }

  Widget _buildExportTypeCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Export Type',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          _buildRadioOption('current', 'Current Session', 'Export only the current session data'),
          _buildRadioOption('all', 'All Sessions', 'Export all historical session data'),
          _buildRadioOption('date_range', 'Date Range', 'Export sessions within a specific date range'),
        ],
      ),
    );
  }

  Widget _buildRadioOption(String value, String title, String description) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: _selectedExportType == value 
            ? Colors.cyan.withOpacity(0.1) 
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _selectedExportType == value 
              ? Colors.cyan.withOpacity(0.3) 
              : Colors.white.withOpacity(0.1),
        ),
      ),
      child: RadioListTile<String>(
        value: value,
        groupValue: _selectedExportType,
        onChanged: (newValue) {
          setState(() => _selectedExportType = newValue!);
        },
        title: Text(title, style: TextStyle(color: Colors.white, fontSize: 16)),
        subtitle: Text(description, style: TextStyle(color: Colors.white70, fontSize: 12)),
        activeColor: Colors.cyan,
        dense: true,
      ),
    );
  }

  Widget _buildFormatSelectionCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Export Format',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildFormatOption('json', 'JSON', Icons.code),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildFormatOption('csv', 'CSV', Icons.table_chart),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFormatOption(String format, String label, IconData icon) {
    final isSelected = _selectedFormat == format;
    return GestureDetector(
      onTap: () => setState(() => _selectedFormat = format),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? Colors.cyan.withOpacity(0.1) : Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.cyan : Colors.white.withOpacity(0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(icon, color: isSelected ? Colors.cyan : Colors.white70, size: 32),
            SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.cyan : Colors.white70,
                fontSize: 16,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Date Range',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDateSelector('Start Date', _startDate, (date) {
                  setState(() => _startDate = date);
                }),
              ),
              SizedBox(width: 16),
              Expanded(
                child: _buildDateSelector('End Date', _endDate, (date) {
                  setState(() => _endDate = date);
                }),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelector(String label, DateTime selectedDate, Function(DateTime) onDateChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(color: Colors.white70, fontSize: 14)),
        SizedBox(height: 8),
        GestureDetector(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: selectedDate,
              firstDate: DateTime(2020),
              lastDate: DateTime.now(),
            );
            if (date != null) onDateChanged(date);
          },
          child: Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.calendar_today, color: Colors.white70, size: 16),
                SizedBox(width: 8),
                Text(
                  '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExportButton() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.cyan, Colors.blue],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.cyan.withOpacity(0.3),
            blurRadius: 15,
            offset: Offset(0, 8),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isExporting ? null : _performExport,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        ),
        child: _isExporting
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 2,
                    ),
                  ),
                  SizedBox(width: 12),
                  Text('Exporting...', style: TextStyle(color: Colors.white, fontSize: 16)),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.download, color: Colors.white),
                  SizedBox(width: 8),
                  Text(
                    'Export Sessions',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildExportGuideCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.white70, size: 20),
              SizedBox(width: 8),
              Text(
                'Export Guide',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          _buildGuideItem('JSON format includes complete behavioral data with nested structures'),
          _buildGuideItem('CSV format provides tabular data suitable for spreadsheet analysis'),
          _buildGuideItem('Files are saved to your device\'s Documents directory'),
          _buildGuideItem('Current session exports real-time behavioral data'),
          _buildGuideItem('Historical exports include all past session analytics'),
        ],
      ),
    );
  }

  Widget _buildGuideItem(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 4,
            height: 4,
            margin: EdgeInsets.only(top: 8, right: 12),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white70,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
