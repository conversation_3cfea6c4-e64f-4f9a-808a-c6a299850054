# Trust Chain SuitCase - App Navigation & Workflow

## 🏗️ **Cleaned Architecture Overview**

### **Core Screens (Professional & Essential)**
1. **`first_screen.dart`** - App entry point with security initialization
2. **`splash_screen.dart`** - Loading screen with branding
3. **`onboarding_screen.dart`** - User introduction and features
4. **`secure_login_screen.dart`** - Advanced login with behavioral tracking
5. **`register_screen.dart`** - User registration
6. **`dashboard_screen.dart`** - Main banking dashboard (clean professional UI)
7. **`activity_screen.dart`** - Transaction history and account activity
8. **`profile_screen.dart`** - User profile and settings

### **Security & Behavioral Screens**
1. **`behavioral_auth_screen.dart`** - Behavioral authentication setup
2. **`privacy_dashboard_screen.dart`** - Privacy settings and data management
3. **`behavioral_data_screen.dart`** - Behavioral data visualization
4. **`home_screen.dart`** - Advanced behavioral monitoring (for logged-in users)
5. **`session_export_screen.dart`** - Data export functionality
6. **`admin_trust_score_screen.dart`** - Trust score management

### **Payment & Banking**
1. **`payment_success_screen.dart`** - Payment confirmation

---

## 🚀 **User Journey & Navigation Flow**

### **1. App Launch Sequence**
```
FirstScreen → SplashScreen → OnboardingScreen → SecureLoginScreen
```

### **2. Authentication Flow**
```
SecureLoginScreen ←→ RegisterScreen
    ↓ (successful login)
BehavioralAuthScreen (if enabled)
    ↓
DashboardScreen (main app)
```

### **3. Main App Navigation**
```
DashboardScreen (Home Tab)
    ├── ActivityScreen (Activity Tab)
    ├── Profile (Profile Tab)
    └── Various Banking Features
```

### **4. Security & Privacy Flow**
```
ProfileScreen
    ├── PrivacyDashboardScreen
    ├── BehavioralDataScreen
    ├── AdminTrustScoreScreen
    ├── SessionExportScreen
    └── SecuritySettings
```

### **5. Advanced Behavioral Monitoring**
```
DashboardScreen → HomeScreen (with behavioral data)
    ├── Real-time panic detection
    ├── Behavioral pattern analysis
    ├── Smart challenge triggers
    └── Continuous authentication
```

---

## 🎯 **Screen Responsibilities**

### **Entry & Authentication**
- **FirstScreen**: Security initialization, app startup
- **SplashScreen**: Branding, loading states
- **OnboardingScreen**: Feature introduction, first-time user guidance
- **SecureLoginScreen**: Primary authentication with behavioral tracking
- **RegisterScreen**: New user registration

### **Main Banking Interface**
- **DashboardScreen**: 
  - Account balance and overview
  - Quick actions (Transfer, Pay Bills, etc.)
  - Recent transactions
  - Bottom navigation hub

### **Security & Privacy**
- **BehavioralAuthScreen**: Setup and configure behavioral authentication
- **PrivacyDashboardScreen**: Privacy settings, data transparency
- **BehavioralDataScreen**: Visualize collected behavioral data
- **HomeScreen**: Advanced real-time behavioral monitoring
- **SessionExportScreen**: Data export and portability

### **Activity & Transactions**
- **ActivityScreen**: Transaction history, account activity
- **PaymentSuccessScreen**: Payment confirmations

### **User Management**
- **ProfileScreen**: User settings, account management, navigation hub

---

## 🔧 **Technical Implementation**

### **Removed Redundant Screens**
- ❌ `login_screen.dart` (replaced by `secure_login_screen.dart`)
- ❌ `login_screen_old.dart` (outdated version)
- ❌ `dashboard_screen.dart` (complex version, replaced by `dashboard_simple.dart`)
- ❌ `test_dashboard.dart` (test file)

### **Route Structure**
All screens are properly registered in `app_routes.dart` with:
- Static route constants
- Route generation with proper parameter handling
- Error handling for unknown routes

### **Navigation Patterns**
1. **Bottom Navigation**: Dashboard → Activity → Profile
2. **Hierarchical Navigation**: Profile → Security Settings
3. **Modal Navigation**: Payment flows, confirmations
4. **Replacement Navigation**: Login → Dashboard (clear stack)

---

## 📱 **User Experience Guidelines**

### **Consistency**
- Unified color scheme (`AppColors.bankingBackground`, `bankingPrimary`)
- Consistent button styles and animations
- Standardized padding and spacing

### **Professional Design**
- Clean, modern banking UI
- Subtle animations and transitions
- Professional typography
- Proper loading states

### **Security-First**
- Behavioral authentication integration
- Privacy-conscious design
- Secure data handling
- Clear security indicators

---

## 🛠️ **Development Workflow**

### **Adding New Screens**
1. Create screen in `/lib/screens/`
2. Add route constant in `AppRoutes`
3. Add route generation in `AppRouter.generateRoute`
4. Update navigation calls in existing screens

### **Behavioral Integration**
- Use `HomeScreen` for advanced behavioral monitoring
- `BehavioralAuthScreen` for setup
- `BehavioralDataScreen` for data visualization
- `PrivacyDashboardScreen` for user control

### **Testing Navigation**
- Test all route transitions
- Verify parameter passing (especially `HomeScreen`)
- Test back button behavior
- Validate bottom navigation state

---

## 🎨 **Design System**

### **Colors**
- Background: `Color(0xFF0D1B2A)`
- Primary: `Color(0xFF4285F4)`
- Secondary: `Color(0xFF1B263B)`

### **Typography**
- Primary Font: 'SF Pro Display'
- Heading: FontWeight.bold
- Body: FontWeight.normal

### **Components**
- Consistent card designs
- Unified button styles
- Professional form inputs
- Modern bottom navigation

---

This architecture provides a clean, professional, and scalable foundation for the Trust Chain banking application with integrated behavioral authentication.
