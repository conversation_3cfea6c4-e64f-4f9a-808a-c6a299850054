import 'dart:async';
import 'package:flutter/material.dart';
import 'package:sensors_plus/sensors_plus.dart';
import '../firebase/firebase_service.dart';
import '../models/behavioral_data_model.dart';
import '../services/panic_detector.dart';
import '../services/challenge_generator.dart';
import '../widgets/smart_popup_widget.dart';
import 'behavioral_data_screen.dart';
import 'session_export_screen.dart';
import 'admin_trust_score_screen.dart';
import 'privacy_dashboard_screen.dart';

class HomeScreen extends StatefulWidget {
  final BehavioralData behavioralData;
  final String userId;

  const HomeScreen({
    required this.behavioralData,
    required this.userId,
    Key? key,
  }) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late Timer _uploadTimer;
  late PanicDetector _panicDetector;
  late ChallengeGenerator _challengeGenerator;
  
  final TextEditingController _hiddenTextController = TextEditingController();
  final FocusNode _hiddenFocusNode = FocusNode();

  int _lastKeystrokeTimestamp = 0;
  Offset? _lastSwipeOffset;
  int _lastSwipeTime = 0;
  int _swipeStartTime = 0;
  
  bool _isSmartChallengeActive = false;
  StreamSubscription? _accelerometerSubscription;

  @override
  @override
  void initState() {
    super.initState();
    
    // Initialize panic detector
    _panicDetector = PanicDetector();
    _panicDetector.initialize();
    
    // Initialize challenge generator
    _challengeGenerator = ChallengeGenerator();
    
    // Listen for panic gestures
    _panicDetector.onPanicDetected = _handlePanicDetected;
    
    // Start accelerometer monitoring for shake detection
    _accelerometerSubscription = accelerometerEventStream().listen((event) {
      _panicDetector.processAccelerometerData(event.x, event.y, event.z);
    });

    // ✅ Ensure the hidden field doesn't trigger the keyboard
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        FocusScope.of(context).unfocus();
        // Do NOT request focus — just keep the field offstage and readOnly
      }
    });

    // Upload behavioral data every 30 seconds
    _uploadTimer = Timer.periodic(const Duration(seconds: 30), (_) async {
      await FirebaseService().uploadBehavioralData(
        widget.userId,
        widget.behavioralData,
      );
      
      // Trigger smart challenge based on risk assessment
      _evaluateSmartChallenge();
    });
  }


  @override
  void dispose() {
    // Upload data one last time before closing
    FirebaseService().uploadBehavioralData(
      widget.userId,
      widget.behavioralData,
    );

    _uploadTimer.cancel();
    _accelerometerSubscription?.cancel();
    _panicDetector.dispose();
    _hiddenTextController.dispose();
    _hiddenFocusNode.dispose();
    super.dispose();
  }


  void _onTapDown(TapDownDetails details) {
    widget.behavioralData.addTapData(
      0.5, // Simulated pressure
      details.globalPosition,
    );
  }

  void _onSwipeUpdate(DragUpdateDetails details) {
  final now = DateTime.now().millisecondsSinceEpoch;

  if (_lastSwipeOffset != null) {
    final delta = details.globalPosition - _lastSwipeOffset!;
    final dt = now - _lastSwipeTime;

    if (dt > 0) {
      final velocity = delta.distance / dt;
      widget.behavioralData.addSwipeVelocity(velocity);

      if (_swipeStartTime > 0) {
        int swipeDuration = now - _swipeStartTime;
        widget.behavioralData.addSwipeDuration(swipeDuration);
      }
    }
  }

  _swipeStartTime = now;
  _lastSwipeOffset = details.globalPosition;
  _lastSwipeTime = now;
}


  void _onHiddenTextChanged(String text) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    if (_lastKeystrokeTimestamp != 0) {
      widget.behavioralData.addKeystroke(timestamp);
    }

    _lastKeystrokeTimestamp = timestamp;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          GestureDetector(
            onTapDown: _onTapDown,
            onPanUpdate: _onSwipeUpdate,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF667eea),
                    Color(0xFF764ba2),
                  ],
                ),
              ),
              child: SafeArea(
                child: Padding(
                  padding: EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Welcome Back!',
                                style: TextStyle(
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              Text(
                                'Your account is secure',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white.withOpacity(0.8),
                                ),
                              ),
                            ],
                          ),
                          Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.notifications_outlined,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 40),

                      // Security Status
                      Container(
                        padding: EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.security,
                                    color: Colors.green, size: 28),
                                SizedBox(width: 12),
                                Flexible(
                                  child: Text(
                                    "Security Status: Active",
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                    style: TextStyle(fontSize: 19,
                                    color: Colors.white,
                                     fontWeight: FontWeight.bold),
                                     ),
                                     )

                              ],
                            ),
                            SizedBox(height: 16),
                            Text(
                              'Behavioral authentication is monitoring your session for continuous security.',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white.withOpacity(0.8),
                              ),
                            ),
                            SizedBox(height: 20),
                            Row(
                              children: [
                                Icon(Icons.check_circle,
                                    color: Colors.green, size: 20),
                                SizedBox(width: 8),
                                Text(
                                  'Identity Verified',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                            SizedBox(height: 8),
                            Row(
                              children: [
                                Icon(Icons.check_circle,
                                    color: Colors.green, size: 20),
                                SizedBox(width: 8),
                                Text(
                                  'Behavioral Pattern Matched',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                            SizedBox(height: 8),
                            Row(
                              children: [
                                Icon(Icons.check_circle,
                                    color: Colors.green, size: 20),
                                SizedBox(width: 8),
                                Text(
                                  'Session Secure',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 30),

                      // Quick Actions
                      Text(
                        'Quick Actions',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 20),

                      Expanded(
                        child: GridView.count(
                          crossAxisCount: 2,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                          children: [
                            _buildActionCard('Transfer Money', Icons.send,
                                const Color.fromARGB(255, 29, 176, 255), () {}),
                            _buildActionCard('View Balance',
                                Icons.account_balance_wallet, Colors.green,
                                () {}),
                            _buildActionCard('Transaction History',
                                Icons.history, const Color.fromARGB(255, 255, 172, 47), () {}),
                            _buildActionCard('Security Settings',
                                Icons.settings_outlined, Colors.purple, () {}),
                            _buildActionCard(
                              'Privacy Dashboard',
                              Icons.privacy_tip_outlined,
                              const Color.fromARGB(255, 63, 81, 181),
                              () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => PrivacyDashboardScreen(),
                                  ),
                                );
                              },
                            ),
                            _buildActionCard(
                              'Behavioral Data',
                              Icons.analytics_outlined,
                              const Color.fromARGB(255, 6, 235, 212),
                              () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => BehavioralDataScreen(
                                      data: widget.behavioralData,
                                    ),
                                  ),
                                );
                              },
                            ),
                            _buildActionCard(
                              'Export Sessions',
                              Icons.file_download_outlined,
                              const Color.fromARGB(255, 255, 87, 34),
                              () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => SessionExportScreen(
                                      userId: widget.userId,
                                      currentSession: widget.behavioralData,
                                    ),
                                  ),
                                );
                              },
                            ),
                            _buildActionCard(
                              'Admin Analytics',
                              Icons.admin_panel_settings_outlined,
                              const Color.fromARGB(255, 156, 39, 176),
                              () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => AdminTrustScoreScreen(
                                      adminId: widget.userId,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Invisible TextField for keystroke tracking
          // Hidden TextField for keystroke tracking
IgnorePointer(
  ignoring: true,
  child: Opacity(
    opacity: 0,
    child: SizedBox(
      height: 0.1,
      width: 0.1,
      child: TextField(
        controller: _hiddenTextController,
        focusNode: _hiddenFocusNode,
        onChanged: _onHiddenTextChanged,
        readOnly: true,              // ✅ Prevents keyboard
        showCursor: false,           // ✅ Hide cursor
        enableInteractiveSelection: false,
      ),
    ),
  ),
),

        ],
      ),
    );
  }

  Widget _buildActionCard(
      String title, IconData icon, Color color, VoidCallback onTap) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, size: 40, color: color),
                SizedBox(height: 12),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handlePanicDetected() {
    if (mounted) {
      // Show panic alert dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              SizedBox(width: 8),
              Text('Panic Detected'),
            ],
          ),
          content: Text(
            'A panic gesture was detected. If this is a genuine emergency, tap "Send Silent Alert". If this was accidental, tap "Cancel".',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                _sendSilentAlert();
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: Text('Send Silent Alert', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      );
    }
  }

  void _sendSilentAlert() async {
    try {
      // In a real app, this would send a silent alert to authorities or emergency contacts
      await FirebaseService().uploadBehavioralData(
        widget.userId,
        widget.behavioralData,
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Silent alert sent. Stay safe.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send alert. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _evaluateSmartChallenge() {
    if (_isSmartChallengeActive) return;
    
    // Simple risk assessment based on behavioral data
    final riskScore = _calculateRiskScore();
    
    if (riskScore > 0.7) {
      _showSmartChallenge();
    }
  }

  double _calculateRiskScore() {
    // Simple risk calculation based on behavioral patterns
    // In a real app, this would use ML models
    double risk = 0.0;
    
    // Check for unusual patterns
    if (widget.behavioralData.swipeVelocities.isNotEmpty) {
      final avgVelocity = widget.behavioralData.swipeVelocities.reduce((a, b) => a + b) / widget.behavioralData.swipeVelocities.length;
      if (avgVelocity > 1000) risk += 0.3; // Unusually fast swipes
    }
    
    if (widget.behavioralData.keystrokeTimings.isNotEmpty) {
      final avgTiming = widget.behavioralData.keystrokeTimings.reduce((a, b) => a + b) / widget.behavioralData.keystrokeTimings.length;
      if (avgTiming < 100) risk += 0.2; // Unusually fast typing
    }
    
    // Random challenge for demo purposes
    if (DateTime.now().millisecondsSinceEpoch % 10 == 0) {
      risk += 0.8;
    }
    
    return risk;
  }

  void _showSmartChallenge() {
    if (!mounted) return;
    
    setState(() {
      _isSmartChallengeActive = true;
    });
    
    final challenge = _challengeGenerator.generateSimpleChallenge();
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => SmartPopupWidget(
        challenge: challenge,
        onChallengeComplete: (result) {
          setState(() {
            _isSmartChallengeActive = false;
          });
          
          if (!result.isCorrect) {
            // Handle failed challenge - could lock account or require additional verification
            _handleFailedChallenge();
          }
        },
      ),
    );
  }

  void _handleFailedChallenge() {
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Security Alert'),
          content: Text('Challenge failed. Please verify your identity through additional security measures.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('OK'),
            ),
          ],
        ),
      );
    }
  }
}
