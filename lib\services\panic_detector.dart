import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math';
import '../firebase/firebase_service.dart';

class PanicDetector {
  static final PanicDetector _instance = PanicDetector._internal();
  factory PanicDetector() => _instance;
  PanicDetector._internal();

  // Panic detection state
  bool _isActive = false;
  bool _isPanicModeActive = false;
  DateTime? _lastShakeTime;
  int _shakeCount = 0;
  Timer? _shakeResetTimer;
  Timer? _volumeButtonTimer;
  int _volumeButtonPressCount = 0;
  
  // Configuration
  static const double _shakeThreshold = 2.5;
  static const int _shakeCountThreshold = 3;
  static const Duration _shakeWindow = Duration(seconds: 3);
  static const Duration _volumeButtonWindow = Duration(seconds: 2);
  static const int _volumeButtonThreshold = 5;
  
  // Callbacks
  Function(PanicEvent)? _onPanicDetected;
  Function(PanicEvent)? _onPanicResolved;
  VoidCallback? _onDuressActivated;
  
  // Getter/Setter for onPanicDetected callback
  set onPanicDetected(VoidCallback? callback) {
    _onPanicDetected = callback != null ? (event) => callback() : null;
  }
  
  // Services
  final FirebaseService _firebaseService = FirebaseService();

  /// Initialize panic detection
  void initialize({
    Function(PanicEvent)? onPanicDetected,
    Function(PanicEvent)? onPanicResolved,
    VoidCallback? onDuressActivated,
  }) {
    _onPanicDetected = onPanicDetected;
    _onPanicResolved = onPanicResolved;
    _onDuressActivated = onDuressActivated;
    
    _setupVolumeButtonListener();
    _isActive = true;
    
    debugPrint('Panic detector initialized');
  }

  /// Process accelerometer data for shake detection
  void processAccelerometerData(double x, double y, double z) {
    if (!_isActive) return;
    
    final acceleration = sqrt(x * x + y * y + z * z);
    
    if (acceleration > _shakeThreshold) {
      _handleShakeDetected();
    }
  }

  /// Handle shake detection
  void _handleShakeDetected() {
    final now = DateTime.now();
    
    if (_lastShakeTime == null || 
        now.difference(_lastShakeTime!) > _shakeWindow) {
      _shakeCount = 1;
    } else {
      _shakeCount++;
    }
    
    _lastShakeTime = now;
    
    // Reset shake count after window
    _shakeResetTimer?.cancel();
    _shakeResetTimer = Timer(_shakeWindow, () {
      _shakeCount = 0;
    });
    
    // Trigger panic if threshold reached
    if (_shakeCount >= _shakeCountThreshold) {
      _triggerPanic(PanicTrigger.shakeGesture);
    }
  }

  /// Setup volume button listener
  void _setupVolumeButtonListener() {
    // Note: This is a simplified version. In a real app, you'd use
    // volume_control or similar plugin to detect volume button presses
    
    // For now, we'll expose a method to manually trigger volume button detection
    // This would be called by the native Android code
  }

  /// Handle volume button press (called from native code)
  void handleVolumeButtonPress() {
    if (!_isActive) return;
    
    _volumeButtonPressCount++;
    
    _volumeButtonTimer?.cancel();
    _volumeButtonTimer = Timer(_volumeButtonWindow, () {
      _volumeButtonPressCount = 0;
    });
    
    if (_volumeButtonPressCount >= _volumeButtonThreshold) {
      _triggerPanic(PanicTrigger.volumeButton);
    }
  }

  /// Trigger panic mode
  void _triggerPanic(PanicTrigger trigger) {
    if (_isPanicModeActive) return;
    
    _isPanicModeActive = true;
    
    final event = PanicEvent(
      trigger: trigger,
      timestamp: DateTime.now(),
      location: 'Unknown', // Would be populated by location service
      deviceId: 'current_device', // Would be populated by device info
    );
    
    _onPanicDetected?.call(event);
    
    // Send silent alert
    _sendSilentAlert(event);
    
    // Trigger haptic feedback
    HapticFeedback.vibrate();
    
    // Auto-resolve after 5 minutes if not manually resolved
    Timer(Duration(minutes: 5), () {
      if (_isPanicModeActive) {
        _resolvePanic();
      }
    });
  }

  /// Send silent alert to emergency contacts/security
  Future<void> _sendSilentAlert(PanicEvent event) async {
    try {
      // Send to Firebase for emergency response
      await _firebaseService.sendEmergencyAlert(
        'panic_alert',
        'panic_alert',
        {
          'type': 'panic_alert',
          'trigger': event.trigger.toString(),
          'timestamp': event.timestamp.toIso8601String(),
          'location': event.location,
          'deviceId': event.deviceId,
          'severity': 'high',
          'silent': true,
        },
      );
      
      debugPrint('Silent alert sent successfully');
    } catch (e) {
      debugPrint('Failed to send silent alert: $e');
    }
  }

  /// Manually trigger panic (for testing or UI button)
  void triggerManualPanic() {
    _triggerPanic(PanicTrigger.manual);
  }

  /// Resolve panic mode
  void _resolvePanic() {
    if (!_isPanicModeActive) return;
    
    _isPanicModeActive = false;
    
    final event = PanicEvent(
      trigger: PanicTrigger.resolved,
      timestamp: DateTime.now(),
      location: 'Unknown',
      deviceId: 'current_device',
    );
    
    _onPanicResolved?.call(event);
    
    // Send resolution notification
    _sendResolutionNotification(event);
  }

  /// Send resolution notification
  Future<void> _sendResolutionNotification(PanicEvent event) async {
    try {
      await _firebaseService.sendEmergencyAlert(
        'panic_resolved',
        'panic_resolved',
        {
          'type': 'panic_resolved',
          'timestamp': event.timestamp.toIso8601String(),
          'deviceId': event.deviceId,
          'severity': 'info',
        },
      );
      
      debugPrint('Panic resolution sent');
    } catch (e) {
      debugPrint('Failed to send resolution: $e');
    }
  }

  /// Activate duress mode (appears normal but sends alerts)
  void activateDuressMode() {
    _onDuressActivated?.call();
    
    // Send duress alert
    _sendDuressAlert();
  }

  /// Send duress alert (user appears to be acting normally but is in distress)
  Future<void> _sendDuressAlert() async {
    try {
      await _firebaseService.sendEmergencyAlert(
        'duress_alert',
        'duress_alert',
        {
          'type': 'duress_alert',
          'timestamp': DateTime.now().toIso8601String(),
          'deviceId': 'current_device',
          'severity': 'critical',
          'silent': true,
          'message': 'User may be under duress - appears normal but triggered alert',
        },
      );
      
      debugPrint('Duress alert sent');
    } catch (e) {
      debugPrint('Failed to send duress alert: $e');
    }
  }

  /// Check if panic mode is active
  bool get isPanicModeActive => _isPanicModeActive;

  /// Stop panic detection
  void stop() {
    _isActive = false;
    _shakeResetTimer?.cancel();
    _volumeButtonTimer?.cancel();
    _isPanicModeActive = false;
  }

  /// Dispose resources
  void dispose() {
    stop();
    _onPanicDetected = null;
    _onPanicResolved = null;
    _onDuressActivated = null;
  }
}

enum PanicTrigger {
  shakeGesture,
  volumeButton,
  manual,
  resolved,
}

class PanicEvent {
  final PanicTrigger trigger;
  final DateTime timestamp;
  final String location;
  final String deviceId;

  PanicEvent({
    required this.trigger,
    required this.timestamp,
    required this.location,
    required this.deviceId,
  });

  Map<String, dynamic> toJson() {
    return {
      'trigger': trigger.toString(),
      'timestamp': timestamp.toIso8601String(),
      'location': location,
      'deviceId': deviceId,
    };
  }
}

class PanicButton extends StatefulWidget {
  final VoidCallback? onPanicTriggered;
  final bool isEmergencyMode;

  const PanicButton({
    Key? key,
    this.onPanicTriggered,
    this.isEmergencyMode = false,
  }) : super(key: key);

  @override
  State<PanicButton> createState() => _PanicButtonState();
}

class _PanicButtonState extends State<PanicButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _colorAnimation = ColorTween(
      begin: Colors.red,
      end: Colors.red[800],
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    if (widget.isEmergencyMode) {
      _animationController.repeat(reverse: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: FloatingActionButton(
            onPressed: () {
              HapticFeedback.heavyImpact();
              _showPanicConfirmation();
            },
            backgroundColor: _colorAnimation.value,
            child: Icon(
              widget.isEmergencyMode ? Icons.warning : Icons.emergency,
              color: Colors.white,
              size: 28,
            ),
          ),
        );
      },
    );
  }

  void _showPanicConfirmation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('Emergency Alert'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'This will send a silent emergency alert to your contacts and security team.',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Text(
              'Are you sure you want to proceed?',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              PanicDetector().triggerManualPanic();
              widget.onPanicTriggered?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('Send Alert'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}
