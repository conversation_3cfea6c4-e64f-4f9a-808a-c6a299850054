import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class AndroidSecurity {
  static const MethodChannel _channel = MethodChannel('android_security');

  /// Prevent screenshots and screen recording on Android
  static Future<void> enableScreenSecurity() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        await _channel.invokeMethod('enableScreenSecurity');
      } catch (e) {
        print('Failed to enable screen security: $e');
      }
    }
  }

  /// Disable screenshot prevention (for development)
  static Future<void> disableScreenSecurity() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        await _channel.invokeMethod('disableScreenSecurity');
      } catch (e) {
        print('Failed to disable screen security: $e');
      }
    }
  }

  /// Check if device is rooted (Android specific)
  static Future<bool> isDeviceRooted() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        return await _channel.invokeMethod('isDeviceRooted') ?? false;
      } catch (e) {
        print('Failed to check root status: $e');
        return false;
      }
    }
    return false;
  }

  /// Check if running on emulator (Android specific)
  static Future<bool> isEmulator() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        return await _channel.invokeMethod('isEmulator') ?? false;
      } catch (e) {
        print('Failed to check emulator status: $e');
        return false;
      }
    }
    return false;
  }

  /// Check for Xposed framework (Android specific)
  static Future<bool> hasXposedFramework() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        return await _channel.invokeMethod('hasXposedFramework') ?? false;
      } catch (e) {
        print('Failed to check Xposed framework: $e');
        return false;
      }
    }
    return false;
  }

  /// Check if developer mode is enabled (Android specific)
  static Future<bool> isDeveloperModeEnabled() async {
    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
      try {
        return await _channel.invokeMethod('isDeveloperModeEnabled') ?? false;
      } catch (e) {
        print('Failed to check developer mode: $e');
        return false;
      }
    }
    return false;
  }
}
