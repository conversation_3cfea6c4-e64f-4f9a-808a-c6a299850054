import android.content.Context
import android.content.pm.PackageManager
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.io.File

class AndroidSecurityPlugin: FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        context = flutterPluginBinding.applicationContext
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "android_security")
        channel.setMethodCallHandler(this)
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "isDeviceRooted" -> {
                result.success(isDeviceRooted())
            }
            "isEmulator" -> {
                result.success(isEmulator())
            }
            "hasXposedFramework" -> {
                result.success(hasXposedFramework())
            }
            "isDeveloperModeEnabled" -> {
                result.success(isDeveloperModeEnabled())
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun isDeviceRooted(): Boolean {
        // Check for common root binaries
        val rootPaths = arrayOf(
            "/system/app/Superuser.apk",
            "/sbin/su",
            "/system/bin/su",
            "/system/xbin/su",
            "/data/local/xbin/su",
            "/data/local/bin/su",
            "/system/sd/xbin/su",
            "/system/bin/failsafe/su",
            "/data/local/su",
            "/su/bin/su"
        )

        for (path in rootPaths) {
            if (File(path).exists()) {
                return true
            }
        }

        // Check for root management apps
        val rootApps = arrayOf(
            "com.noshufou.android.su",
            "com.noshufou.android.su.elite",
            "eu.chainfire.supersu",
            "com.koushikdutta.superuser",
            "com.thirdparty.superuser",
            "com.yellowes.su",
            "com.topjohnwu.magisk",
            "com.kingroot.kinguser",
            "com.kingo.root",
            "com.smedialink.oneclickroot",
            "com.zhiqupk.root.global",
            "com.alephzain.framaroot"
        )

        for (app in rootApps) {
            try {
                context.packageManager.getPackageInfo(app, 0)
                return true
            } catch (e: PackageManager.NameNotFoundException) {
                // App not found, continue checking
            }
        }

        // Check for build properties
        val buildTags = android.os.Build.TAGS
        return buildTags != null && buildTags.contains("test-keys")
    }

    private fun isEmulator(): Boolean {
        return (android.os.Build.FINGERPRINT.startsWith("generic") ||
                android.os.Build.FINGERPRINT.lowercase().contains("vbox") ||
                android.os.Build.FINGERPRINT.lowercase().contains("test-keys") ||
                android.os.Build.MODEL.contains("google_sdk") ||
                android.os.Build.MODEL.contains("Emulator") ||
                android.os.Build.MODEL.contains("Android SDK built for x86") ||
                android.os.Build.MANUFACTURER.contains("Genymotion") ||
                android.os.Build.HARDWARE == "goldfish" ||
                android.os.Build.HARDWARE == "ranchu" ||
                android.os.Build.PRODUCT == "sdk" ||
                android.os.Build.PRODUCT == "google_sdk" ||
                android.os.Build.PRODUCT == "sdk_x86" ||
                android.os.Build.PRODUCT == "vbox86p" ||
                android.os.Build.BOARD.lowercase().contains("nox") ||
                android.os.Build.BOOTLOADER.lowercase().contains("nox") ||
                android.os.Build.HARDWARE.lowercase().contains("nox") ||
                android.os.Build.PRODUCT.lowercase().contains("nox"))
    }

    private fun hasXposedFramework(): Boolean {
        try {
            val xposedBridge = Class.forName("de.robv.android.xposed.XposedBridge")
            return xposedBridge != null
        } catch (e: ClassNotFoundException) {
            // Xposed not found
        }

        // Check for Xposed installer
        try {
            context.packageManager.getPackageInfo("de.robv.android.xposed.installer", 0)
            return true
        } catch (e: PackageManager.NameNotFoundException) {
            // Xposed installer not found
        }

        return false
    }

    private fun isDeveloperModeEnabled(): Boolean {
        return try {
            android.provider.Settings.Secure.getInt(
                context.contentResolver,
                android.provider.Settings.Global.DEVELOPMENT_SETTINGS_ENABLED
            ) != 0
        } catch (e: Exception) {
            false
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }
}
