import 'package:flutter/material.dart';
import '../screens/payment_success_screen.dart';

class NavigationHelper {
  static void showPaymentSuccess(
    BuildContext context, {
    String? amount,
    String? recipient,
    String? transactionId,
    String? paymentCode,
    String? merchantName,
    String? date,
    String? time,
    String? discount,
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentSuccessScreen(
          amount: amount ?? '\$12,728.72',
          recipient: recipient ?? '<PERSON><PERSON><PERSON>',
          transactionId: transactionId ?? '123190310923122',
          paymentCode: paymentCode ?? 'PAY-001822',
          merchantName: merchantName ?? 'iBox Indonesia',
          date: date ?? 'March 23, 2023',
          time: time ?? '08:30 PM',
          discount: discount ?? '\$59.00',
        ),
      ),
    );
  }
}
