import 'package:shared_preferences/shared_preferences.dart';

class AuthRepository {
  Future<void> registerUser(String email, String password) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_email', email);
    await prefs.setString('user_password', password);
  }

  Future<bool> isUserRegistered(String email, String password) async {
    final prefs = await SharedPreferences.getInstance();
    final storedEmail = prefs.getString('user_email');
    final storedPassword = prefs.getString('user_password');
    return email == storedEmail && password == storedPassword;
  }

  Future<bool> isAccountExists(String email) async {
    final prefs = await SharedPreferences.getInstance();
    final storedEmail = prefs.getString('user_email');
    return email == storedEmail;
  }
}
