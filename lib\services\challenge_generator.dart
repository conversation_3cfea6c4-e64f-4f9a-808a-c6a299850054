import 'dart:math';
import 'dart:async';
import 'package:flutter/foundation.dart';

class ChallengeGenerator {
  static final ChallengeGenerator _instance = ChallengeGenerator._internal();
  factory ChallengeGenerator() => _instance;
  ChallengeGenerator._internal();

  final Random _random = Random();
  Timer? _challengeTimer;
  
  // Challenge categories for banking app
  final List<ChallengeCategory> _challengeCategories = [
    ChallengeCategory(
      id: 'time_awareness',
      name: 'Time Awareness',
      questions: [
        'What time of day do you typically log into your banking app?',
        'Are you logging in during your usual banking hours?',
        'Is this a typical day for you to check your account?',
      ],
      options: [
        ['Morning (6-10 AM)', 'Afternoon (10 AM-4 PM)', 'Evening (4-10 PM)', 'Night (10 PM-6 AM)'],
        ['Yes, this is normal', 'No, this is unusual', 'I\'m not sure'],
        ['Yes, typical day', 'No, unusual day', 'Special circumstances'],
      ],
    ),
    ChallengeCategory(
      id: 'location_context',
      name: 'Location Context',
      questions: [
        'Where are you typically when you access your banking app?',
        'Are you in a familiar location right now?',
        'Is this a secure location for banking?',
      ],
      options: [
        ['Home', 'Work', 'Public place', 'Other'],
        ['Yes, familiar location', 'No, new location', 'Somewhat familiar'],
        ['Yes, very secure', 'Moderately secure', 'Not very secure'],
      ],
    ),
    ChallengeCategory(
      id: 'behavioral_pattern',
      name: 'Behavioral Pattern',
      questions: [
        'How often do you typically check your account balance?',
        'What type of banking do you usually do on mobile?',
        'Are you planning to make any transactions today?',
      ],
      options: [
        ['Daily', 'Weekly', 'Monthly', 'Rarely'],
        ['Check balance', 'Transfer money', 'Pay bills', 'All of the above'],
        ['Yes, planned transactions', 'No, just checking', 'Maybe, depends on balance'],
      ],
    ),
    ChallengeCategory(
      id: 'device_context',
      name: 'Device Context',
      questions: [
        'Are you using your usual device for banking?',
        'Is this device shared with anyone else?',
        'When did you last update your banking app?',
      ],
      options: [
        ['Yes, my usual device', 'No, different device', 'One of my devices'],
        ['No, only I use it', 'Yes, shared device', 'Sometimes shared'],
        ['Recently (this week)', 'A while ago', 'I don\'t remember'],
      ],
    ),
  ];

  /// Generate a contextual challenge based on risk score
  ChallengeData generateChallenge(double riskScore, Map<String, dynamic> context) {
    // Higher risk = more specific challenges
    final category = _selectCategoryByRisk(riskScore);
    final questionIndex = _random.nextInt(category.questions.length);
    
    return ChallengeData(
      id: '${category.id}_${DateTime.now().millisecondsSinceEpoch}',
      category: category.id,
      question: category.questions[questionIndex],
      options: category.options[questionIndex],
      correctAnswerIndex: _determineCorrectAnswer(category, questionIndex, context),
      riskScore: riskScore,
      timestamp: DateTime.now(),
      timeoutSeconds: _getTimeoutByRisk(riskScore),
    );
  }

  /// Generate a simple challenge with default parameters
  ChallengeData generateSimpleChallenge() {
    return generateChallenge(0.5, {});
  }

  /// Generate multiple challenges for high-risk situations
  Future<List<ChallengeData>> generateChallengeSequence(double riskScore, Map<String, dynamic> context) async {
    if (riskScore < 0.3) return [];
    
    final challenges = <ChallengeData>[];
    final numChallenges = riskScore > 0.8 ? 3 : riskScore > 0.6 ? 2 : 1;
    
    for (int i = 0; i < numChallenges; i++) {
      challenges.add(generateChallenge(riskScore, context));
      // Ensure variety in challenge types
      if (i < numChallenges - 1) {
        await Future.delayed(Duration(milliseconds: 100));
      }
    }
    
    return challenges;
  }

  /// Schedule periodic challenges based on behavioral patterns
  void schedulePeriodicChallenges({
    required Function(ChallengeData) onChallenge,
    required double baseTrustScore,
    Duration interval = const Duration(minutes: 30),
  }) {
    _challengeTimer?.cancel();
    
    _challengeTimer = Timer.periodic(interval, (timer) {
      // Only challenge if trust score is declining
      if (baseTrustScore < 0.7) {
        final challenge = generateChallenge(
          1.0 - baseTrustScore,
          {'type': 'periodic', 'interval': interval.inMinutes}
        );
        onChallenge(challenge);
      }
    });
  }

  void stopPeriodicChallenges() {
    _challengeTimer?.cancel();
    _challengeTimer = null;
  }

  // Private helper methods
  ChallengeCategory _selectCategoryByRisk(double riskScore) {
    if (riskScore > 0.8) {
      // High risk: use device and behavioral patterns
      return _challengeCategories[_random.nextInt(2) + 2];
    } else if (riskScore > 0.5) {
      // Medium risk: use time and location context
      return _challengeCategories[_random.nextInt(2)];
    } else {
      // Low risk: any category
      return _challengeCategories[_random.nextInt(_challengeCategories.length)];
    }
  }

  int _getTimeoutByRisk(double riskScore) {
    if (riskScore > 0.8) return 60; // 1 minute for high risk
    if (riskScore > 0.5) return 90; // 1.5 minutes for medium risk
    return 120; // 2 minutes for low risk
  }

  int? _determineCorrectAnswer(ChallengeCategory category, int questionIndex, Map<String, dynamic> context) {
    // In a real implementation, this would use ML model predictions
    // For now, we return null to indicate user knowledge verification
    return null;
  }
}

class ChallengeCategory {
  final String id;
  final String name;
  final List<String> questions;
  final List<List<String>> options;

  ChallengeCategory({
    required this.id,
    required this.name,
    required this.questions,
    required this.options,
  });
}

class ChallengeData {
  final String id;
  final String category;
  final String question;
  final List<String> options;
  final int? correctAnswerIndex;
  final double riskScore;
  final DateTime timestamp;
  final int timeoutSeconds;

  ChallengeData({
    required this.id,
    required this.category,
    required this.question,
    required this.options,
    this.correctAnswerIndex,
    required this.riskScore,
    required this.timestamp,
    required this.timeoutSeconds,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'question': question,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'riskScore': riskScore,
      'timestamp': timestamp.toIso8601String(),
      'timeoutSeconds': timeoutSeconds,
    };
  }
}

class ChallengeResult {
  final String challengeId;
  final int selectedAnswer;
  final bool isCorrect;
  final Duration responseTime;
  final DateTime timestamp;

  ChallengeResult({
    required this.challengeId,
    required this.selectedAnswer,
    required this.isCorrect,
    required this.responseTime,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'challengeId': challengeId,
      'selectedAnswer': selectedAnswer,
      'isCorrect': isCorrect,
      'responseTime': responseTime.inMilliseconds,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
