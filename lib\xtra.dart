// import 'dart:io';
// import 'dart:convert';

// import 'package:flutter/material.dart';
// import 'dart:math' as math;
// import 'dart:async';

// void main() {
//   runApp(SentinelAuthApp());
// }

// class SentinelAuthApp extends StatelessWidget {
//   const SentinelAuthApp({super.key});
//   @override
//   Widget build(BuildContext context) {
//     return MaterialApp(
//       title: 'SentinelAuth Banking',
//       theme: ThemeData(
//         primarySwatch: Colors.blue,
//         fontFamily: 'SF Pro Display',
//         visualDensity: VisualDensity.adaptivePlatformDensity,
//       ),
//       home: LoginScreen(),
//       debugShowCheckedModeBanner: false,
//     );
//   }
// }

// class BehavioralData {
//   List<int> keystrokeTimes = [];
//   List<double> tapPressures = [];
//   List<Offset> tapPositions = [];
//   List<double> swipeVelocities = [];
//   int sessionStartTime = DateTime.now().millisecondsSinceEpoch;
  
//   void addKeystroke(int timestamp) {
//     keystrokeTimes.add(timestamp);
//   }
  
//   void addTapData(double pressure, Offset position) {
//     tapPressures.add(pressure);
//     tapPositions.add(position);
//   }
  
//   void addSwipeVelocity(double velocity) {
//     swipeVelocities.add(velocity);
//   }
  
//   Map<String, dynamic> toJson() {
//     return {
//       'keystroke_intervals': _calculateIntervals(keystrokeTimes),
//       'avg_tap_pressure': tapPressures.isEmpty ? 0 : tapPressures.reduce((a, b) => a + b) / tapPressures.length,
//       'tap_positions': tapPositions.map((pos) => {'x': pos.dx, 'y': pos.dy}).toList(),
//       'avg_swipe_velocity': swipeVelocities.isEmpty ? 0 : swipeVelocities.reduce((a, b) => a + b) / swipeVelocities.length,
//       'session_duration': DateTime.now().millisecondsSinceEpoch - sessionStartTime,
//     };
//   }
  
//   List<int> _calculateIntervals(List<int> times) {
//     if (times.length < 2) return [];
//     List<int> intervals = [];
//     for (int i = 1; i < times.length; i++) {
//       intervals.add(times[i] - times[i-1]);
//     }
//     return intervals;
//   }
// }

// class LoginScreen extends StatefulWidget { 
//   const LoginScreen({super.key});
  
//   @override
//   _LoginScreenState createState() => _LoginScreenState();
// }

// class _LoginScreenState extends State<LoginScreen> with TickerProviderStateMixin {
//   final TextEditingController _emailController = TextEditingController();
//   final TextEditingController _passwordController = TextEditingController();
//   final FocusNode _emailFocusNode = FocusNode();
//   final FocusNode _passwordFocusNode = FocusNode();
  
//   late AnimationController _animationController;
//   late AnimationController _pulseController;
//   late Animation<double> _slideAnimation;
//   late Animation<double> _fadeAnimation;
//   late Animation<double> _pulseAnimation;
  
//   BehavioralData behavioralData = BehavioralData();
//   bool _isLoading = false;
//   bool _showBiometric = false;
//   double _trustScore = 0.0;
  
//   @override
//   void initState() {
//     super.initState();
    
//     _animationController = AnimationController(
//       duration: Duration(milliseconds: 1500),
//       vsync: this,
//     );
    
//     _pulseController = AnimationController(
//       duration: Duration(milliseconds: 2000),
//       vsync: this,
//     )..repeat();
    
//     _slideAnimation = Tween<double>(
//       begin: 100.0,
//       end: 0.0,
//     ).animate(CurvedAnimation(
//       parent: _animationController,
//       curve: Curves.easeOutBack,
//     ));
    
//     _fadeAnimation = Tween<double>(
//       begin: 0.0,
//       end: 1.0,
//     ).animate(CurvedAnimation(
//       parent: _animationController,
//       curve: Curves.easeOut,
//     ));
    
//     _pulseAnimation = Tween<double>(
//       begin: 0.8,
//       end: 1.2,
//     ).animate(CurvedAnimation(
//       parent: _pulseController,
//       curve: Curves.easeInOut,
//     ));
    
//     _animationController.forward();
    
//     // Simulate trust score calculation
//     Timer.periodic(Duration(milliseconds: 500), (timer) {
//       if (mounted) {
//         setState(() {
//           _trustScore = math.min(1.0, _trustScore + 0.1);
//         });
//       }
//       if (_trustScore >= 1.0) timer.cancel();
//     });
//   }
  
//   @override
//   void dispose() {
//     _animationController.dispose();
//     _pulseController.dispose();
//     _emailController.dispose();
//     _passwordController.dispose();
//     _emailFocusNode.dispose();
//     _passwordFocusNode.dispose();
//     super.dispose();
//   }
  
//   void _onTextChanged(String text, TextEditingController controller) {
//     int currentTime = DateTime.now().millisecondsSinceEpoch;
//     behavioralData.addKeystroke(currentTime);
    
//     // Simulate keystroke dynamics analysis
//     if (behavioralData.keystrokeTimes.length > 3) {
//       setState(() {
//         _trustScore = math.min(1.0, _trustScore + 0.05);
//       });
//     }
//   }
  
//   void _onTapDown(TapDownDetails details) {
//     behavioralData.addTapData(
//       details.globalPosition.distance, // Simulated pressure
//       details.globalPosition,
//     );
//   }

//   Map<String, dynamic> exportBehavioralDataToJson(BehavioralData data) {
//   return {
//     'keystrokeTimes': data.keystrokeTimes.map((t) => t.toString()).toList(),
//     'tapPressures': data.tapPressures,
//     'tapPositions': data.tapPositions
//         .map((pos) => {'dx': pos.dx, 'dy': pos.dy})
//         .toList(),
//     'swipeVelocities': data.swipeVelocities,
//     'sessionStartTime': data.sessionStartTime.toString(),
//   };
// }

// // Future<void> saveJsonToFile() async {
// //   final data = exportBehavioralDataToJson(behavioralData);
// //   final jsonString = jsonEncode(data);

// //   // final directory = await getApplicationDocumentsDirectory();
// //   final file = File('${directory.path}/behavioral_data.json');
// //   await file.writeAsString(jsonString);

// //   print('✅ JSON file saved at: ${file.path}');
// // }

// // Future<void> saveCsvToFile() async {
// //   final directory = await getApplicationDocumentsDirectory();
// //   final file = File('${directory.path}/behavioral_data.csv');

// //   final buffer = StringBuffer();
// //   buffer.writeln('KeystrokeTime,TapPressure,TapX,TapY');

// //   final int maxLen = [
// //     behavioralData.keystrokeTimes.length,
// //     behavioralData.tapPressures.length,
// //     behavioralData.tapPositions.length,
// //   ].reduce((a, b) => a > b ? a : b);

// //   for (int i = 0; i < maxLen; i++) {
// //     final time = i < behavioralData.keystrokeTimes.length
// //         ? behavioralData.keystrokeTimes[i].toString()
// //         : '';
// //     final pressure = i < behavioralData.tapPressures.length
// //         ? behavioralData.tapPressures[i].toString()
// //         : '';
// //     final pos = i < behavioralData.tapPositions.length
// //         ? behavioralData.tapPositions[i]
// //         : Offset.zero;

// //     buffer.writeln('$time,$pressure,${pos.dx},${pos.dy}');
// //   }

// //   await file.writeAsString(buffer.toString());
// //   print('✅ CSV file saved at: ${file.path}');
// // }

  
//   Future<void> _performLogin() async {
//     setState(() {
//       _isLoading = true;
//     });
    
//     // Simulate behavioral analysis
//     await Future.delayed(Duration(milliseconds: 1500));
    
//     // Analyze behavioral data
//     Map<String, dynamic> analysis = behavioralData.toJson();
//     print('Behavioral Analysis: $analysis');

//     // await saveJsonToFile();
    
//     // Simulate ML model prediction
//     double behavioralScore = _calculateBehavioralScore();
    
//     if (behavioralScore > 0.75) {
//       // High confidence - direct login
//       _navigateToHome();
//     } else if (behavioralScore > 0.5) {
//       // Medium confidence - step-up authentication
//       setState(() {
//         _showBiometric = true;
//         _isLoading = false;
//       });
//     } else {
//       // Low confidence - deny access
//       _showSecurityAlert();
//     }
//   }
  
//   double _calculateBehavioralScore() {
//     // Simulate behavioral scoring algorithm
//     double score = 0.0;
    
//     // Keystroke timing consistency
//     if (behavioralData.keystrokeTimes.length > 5) {
//       score += 0.3;
//     }
    
//     // Tap pressure consistency
//     if (behavioralData.tapPressures.isNotEmpty) {
//       double variance = _calculateVariance(behavioralData.tapPressures);
//       score += variance < 50 ? 0.3 : 0.1;
//     }
    
//     // Session duration (reasonable time)
//     int sessionDuration = DateTime.now().millisecondsSinceEpoch - behavioralData.sessionStartTime;
//     if (sessionDuration > 10000 && sessionDuration < 120000) {
//       score += 0.4;
//     }
    
//     return math.min(1.0, score);
//   }
  
//   double _calculateVariance(List<double> values) {
//     if (values.isEmpty) return 0.0;
//     double mean = values.reduce((a, b) => a + b) / values.length;
//     double variance = values.map((x) => math.pow(x - mean, 2)).reduce((a, b) => a + b) / values.length;
//     return variance;
//   }
  
//   void _navigateToHome() {
//     Navigator.pushReplacement(
//       context,
//       MaterialPageRoute(builder: (context) => HomeScreen()),
//     );
//   }
  
//   void _showSecurityAlert() {
//     setState(() {
//       _isLoading = false;
//     });
    
//     showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
//         title: Row(
//           children: [
//             Icon(Icons.security, color: Colors.red),
//             SizedBox(width: 10),
//             Text('Security Alert'),
//           ],
//         ),
//         content: Text(
//           'Unusual behavioral pattern detected. Please verify your identity through additional authentication.',
//           style: TextStyle(fontSize: 16),
//         ),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.of(context).pop(),
//             child: Text('Try Again'),
//           ),
//           ElevatedButton(
//             onPressed: () {
//               Navigator.of(context).pop();
//               _performBiometricAuth();
//             },
//             child: Text('Verify Identity'),
//           ),
//         ],
//       ),
//     );
//   }
  
//   void _performBiometricAuth() {
//     setState(() {
//       _showBiometric = true;
//     });
    
//     // Simulate biometric authentication
//     Future.delayed(Duration(milliseconds: 2000), () {
//       if (mounted) {
//         setState(() {
//           _showBiometric = false;
//         });
//         _navigateToHome();
//       }
//     });
//   }
  
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: GestureDetector(
//         onTapDown: _onTapDown,
//         child: Container(
//           decoration: BoxDecoration(
//             gradient: LinearGradient(
//               begin: Alignment.topLeft,
//               end: Alignment.bottomRight,
//               colors: [
//                 Color(0xFF667eea),
//                 Color(0xFF764ba2),
//                 Color(0xFFf093fb),
//               ],
//             ),
//           ),
//           child: SafeArea(
//             child: SingleChildScrollView(
//               child: Padding(
//                 padding: EdgeInsets.symmetric(horizontal: 24.0),
//                 child: Column(
//                   children: [
//                     SizedBox(height: 60),
                    
//                     // Animated Logo
//                     AnimatedBuilder(
//                       animation: _pulseAnimation,
//                       builder: (context, child) {
//                         return Transform.scale(
//                           scale: _pulseAnimation.value,
//                           child: Container(
//                             width: 120,
//                             height: 120,
//                             decoration: BoxDecoration(
//                               shape: BoxShape.circle,
//                               gradient: LinearGradient(
//                                 colors: [
//                                   Colors.white.withOpacity(0.3),
//                                   Colors.white.withOpacity(0.1),
//                                 ],
//                               ),
//                               boxShadow: [
//                                 BoxShadow(
//                                   color: Colors.white.withOpacity(0.3),
//                                   blurRadius: 20,
//                                   spreadRadius: 5,
//                                 ),
//                               ],
//                             ),
//                             child: Icon(
//                               Icons.security,
//                               size: 60,
//                               color: Colors.white,
//                             ),
//                           ),
//                         );
//                       },
//                     ),
                    
//                     SizedBox(height: 30),
                    
//                     // App Title
//                     AnimatedBuilder(
//                       animation: _fadeAnimation,
//                       builder: (context, child) {
//                         return Opacity(
//                           opacity: _fadeAnimation.value,
//                           child: Column(
//                             children: [
//                               Text(
//                                 'SentinelAuth',
//                                 style: TextStyle(
//                                   fontSize: 32,
//                                   fontWeight: FontWeight.bold,
//                                   color: Colors.white,
//                                   letterSpacing: 1.5,
//                                 ),
//                               ),
//                               SizedBox(height: 8),
//                               Text(
//                                 'Next-Gen Banking Security',
//                                 style: TextStyle(
//                                   fontSize: 16,
//                                   color: Colors.white.withOpacity(0.8),
//                                   letterSpacing: 0.5,
//                                 ),
//                               ),
//                             ],
//                           ),
//                         );
//                       },
//                     ),
                    
//                     SizedBox(height: 50),
                    
//                     // Trust Score Indicator
//                     Container(
//                       padding: EdgeInsets.all(20),
//                       decoration: BoxDecoration(
//                         color: Colors.white.withOpacity(0.1),
//                         borderRadius: BorderRadius.circular(20),
//                         border: Border.all(
//                           color: Colors.white.withOpacity(0.3),
//                           width: 1,
//                         ),
//                       ),
//                       child: Column(
//                         children: [
//                           Row(
//                             children: [
//                               Icon(Icons.psychology, color: Colors.white, size: 20),
//                               SizedBox(width: 10),
//                               Text(
//                                 'Behavioral Trust Score',
//                                 style: TextStyle(
//                                   color: Colors.white,
//                                   fontSize: 16,
//                                   fontWeight: FontWeight.w600,
//                                 ),
//                               ),
//                             ],
//                           ),
//                           SizedBox(height: 15),
//                           LinearProgressIndicator(
//                             value: _trustScore,
//                             backgroundColor: Colors.white.withOpacity(0.2),
//                             valueColor: AlwaysStoppedAnimation<Color>(
//                               _trustScore > 0.7 ? Colors.green : 
//                               _trustScore > 0.4 ? Colors.orange : Colors.red,
//                             ),
//                           ),
//                           SizedBox(height: 10),
//                           Text(
//                             '${(_trustScore * 100).toInt()}% Confidence',
//                             style: TextStyle(
//                               color: Colors.white.withOpacity(0.9),
//                               fontSize: 14,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
                    
//                     SizedBox(height: 40),
                    
//                     // Login Form
//                     AnimatedBuilder(
//                       animation: _slideAnimation,
//                       builder: (context, child) {
//                         return Transform.translate(
//                           offset: Offset(0, _slideAnimation.value),
//                           child: Container(
//                             decoration: BoxDecoration(
//                               color: Colors.white.withOpacity(0.1),
//                               borderRadius: BorderRadius.circular(25),
//                               border: Border.all(
//                                 color: Colors.white.withOpacity(0.3),
//                                 width: 1,
//                               ),
//                             ),
//                             child: Padding(
//                               padding: EdgeInsets.all(30),
//                               child: Column(
//                                 children: [
//                                   // Email Field
//                                   Container(
//                                     decoration: BoxDecoration(
//                                       color: Colors.white.withOpacity(0.1),
//                                       borderRadius: BorderRadius.circular(15),
//                                       border: Border.all(
//                                         color: Colors.white.withOpacity(0.3),
//                                       ),
//                                     ),
//                                     child: TextField(
//                                       controller: _emailController,
//                                       focusNode: _emailFocusNode,
//                                       onChanged: (text) => _onTextChanged(text, _emailController),
//                                       style: TextStyle(color: Colors.white),
//                                       decoration: InputDecoration(
//                                         hintText: 'Email or Username',
//                                         hintStyle: TextStyle(
//                                           color: Colors.white.withOpacity(0.7),
//                                         ),
//                                         prefixIcon: Icon(
//                                           Icons.person_outline,
//                                           color: Colors.white.withOpacity(0.8),
//                                         ),
//                                         border: InputBorder.none,
//                                         contentPadding: EdgeInsets.symmetric(
//                                           horizontal: 20,
//                                           vertical: 16,
//                                         ),
//                                       ),
//                                     ),
//                                   ),
                                  
//                                   SizedBox(height: 20),
                                  
//                                   // Password Field
//                                   Container(
//                                     decoration: BoxDecoration(
//                                       color: Colors.white.withOpacity(0.1),
//                                       borderRadius: BorderRadius.circular(15),
//                                       border: Border.all(
//                                         color: Colors.white.withOpacity(0.3),
//                                       ),
//                                     ),
//                                     child: TextField(
//                                       controller: _passwordController,
//                                       focusNode: _passwordFocusNode,
//                                       onChanged: (text) => _onTextChanged(text, _passwordController),
//                                       obscureText: true,
//                                       style: TextStyle(color: Colors.white),
//                                       decoration: InputDecoration(
//                                         hintText: 'Password',
//                                         hintStyle: TextStyle(
//                                           color: Colors.white.withOpacity(0.7),
//                                         ),
//                                         prefixIcon: Icon(
//                                           Icons.lock_outline,
//                                           color: Colors.white.withOpacity(0.8),
//                                         ),
//                                         border: InputBorder.none,
//                                         contentPadding: EdgeInsets.symmetric(
//                                           horizontal: 20,
//                                           vertical: 16,
//                                         ),
//                                       ),
//                                     ),
//                                   ),
                                  
//                                   SizedBox(height: 30),
                                  
//                                   // Login Button
//                                   Container(
//                                     width: double.infinity,
//                                     height: 55,
//                                     decoration: BoxDecoration(
//                                       borderRadius: BorderRadius.circular(15),
//                                       gradient: LinearGradient(
//                                         colors: [
//                                           Color(0xFF4facfe),
//                                           Color(0xFF00f2fe),
//                                         ],
//                                       ),
//                                       boxShadow: [
//                                         BoxShadow(
//                                           color: Color(0xFF4facfe).withOpacity(0.3),
//                                           blurRadius: 15,
//                                           offset: Offset(0, 8),
//                                         ),
//                                       ],
//                                     ),
//                                     child: ElevatedButton(
//                                       onPressed: _isLoading ? null : _performLogin,
//                                       style: ElevatedButton.styleFrom(
//                                         backgroundColor: Colors.transparent,
//                                         shadowColor: Colors.transparent,
//                                         shape: RoundedRectangleBorder(
//                                           borderRadius: BorderRadius.circular(15),
//                                         ),
//                                       ),
//                                       child: _isLoading
//                                           ? SizedBox(
//                                               width: 25,
//                                               height: 25,
//                                               child: CircularProgressIndicator(
//                                                 valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
//                                                 strokeWidth: 2,
//                                               ),
//                                             )
//                                           : Text(
//                                               'Secure Login',
//                                               style: TextStyle(
//                                                 fontSize: 18,
//                                                 fontWeight: FontWeight.w600,
//                                                 color: Colors.white,
//                                               ),
//                                             ),
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ),
//                         );
//                       },
//                     ),
                    
//                     SizedBox(height: 30),
                    
//                     // Biometric Authentication Overlay
//                     if (_showBiometric)
//                       Container(
//                         padding: EdgeInsets.all(30),
//                         decoration: BoxDecoration(
//                           color: Colors.white.withOpacity(0.1),
//                           borderRadius: BorderRadius.circular(20),
//                           border: Border.all(
//                             color: Colors.white.withOpacity(0.3),
//                           ),
//                         ),
//                         child: Column(
//                           children: [
//                             Icon(
//                               Icons.fingerprint,
//                               size: 80,
//                               color: Colors.white,
//                             ),
//                             SizedBox(height: 20),
//                             Text(
//                               'Additional Verification Required',
//                               style: TextStyle(
//                                 color: Colors.white,
//                                 fontSize: 18,
//                                 fontWeight: FontWeight.w600,
//                               ),
//                             ),
//                             SizedBox(height: 10),
//                             Text(
//                               'Please place your finger on the sensor',
//                               style: TextStyle(
//                                 color: Colors.white.withOpacity(0.8),
//                                 fontSize: 14,
//                               ),
//                             ),
//                             SizedBox(height: 20),
//                             CircularProgressIndicator(
//                               valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
//                             ),
//                           ],
//                         ),
//                       ),
                    
//                     SizedBox(height: 30),
                    
//                     // Footer
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Icon(
//                           Icons.shield_outlined,
//                           color: Colors.white.withOpacity(0.6),
//                           size: 16,
//                         ),
//                         SizedBox(width: 8),
//                         Text(
//                           'Protected by AI-Powered Security',
//                           style: TextStyle(
//                             color: Colors.white.withOpacity(0.6),
//                             fontSize: 12,
//                           ),
//                         ),
//                       ],
//                     ),
                    
//                     SizedBox(height: 40),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

// class HomeScreen extends StatelessWidget {
//   const HomeScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Container(
//         decoration: BoxDecoration(
//           gradient: LinearGradient(
//             begin: Alignment.topLeft,
//             end: Alignment.bottomRight,
//             colors: [
//               Color(0xFF667eea),
//               Color(0xFF764ba2),
//             ],
//           ),
//         ),
//         child: SafeArea(
//           child: Padding(
//             padding: EdgeInsets.all(24.0),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           'Welcome Back!',
//                           style: TextStyle(
//                             fontSize: 28,
//                             fontWeight: FontWeight.bold,
//                             color: Colors.white,
//                           ),
//                         ),
//                         Text(
//                           'Your account is secure',
//                           style: TextStyle(
//                             fontSize: 16,
//                             color: Colors.white.withOpacity(0.8),
//                           ),
//                         ),
//                       ],
//                     ),
//                     Container(
//                       padding: EdgeInsets.all(12),
//                       decoration: BoxDecoration(
//                         color: Colors.white.withOpacity(0.2),
//                         shape: BoxShape.circle,
//                       ),
//                       child: Icon(
//                         Icons.notifications_outlined,
//                         color: Colors.white,
//                         size: 24,
//                       ),
//                     ),
//                   ],
//                 ),
                
//                 SizedBox(height: 40),
                
//                 // Security Status Card
//                 Container(
//                   padding: EdgeInsets.all(24),
//                   decoration: BoxDecoration(
//                     color: Colors.white.withOpacity(0.1),
//                     borderRadius: BorderRadius.circular(20),
//                     border: Border.all(
//                       color: Colors.white.withOpacity(0.3),
//                     ),
//                   ),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Row(
//                         children: [
//                           Icon(
//                             Icons.security,
//                             color: Colors.green,
//                             size: 28,
//                           ),
//                           SizedBox(width: 12),
//                           Text(
//                             'Security Status: ACTIVE',
//                             style: TextStyle(
//                               fontSize: 20,
//                               fontWeight: FontWeight.bold,
//                               color: Colors.white,
//                             ),
//                           ),
//                         ],
//                       ),
//                       SizedBox(height: 16),
//                       Text(
//                         'Behavioral authentication is monitoring your session for continuous security.',
//                         style: TextStyle(
//                           fontSize: 16,
//                           color: Colors.white.withOpacity(0.8),
//                         ),
//                       ),
//                       SizedBox(height: 20),
//                       Row(
//                         children: [
//                           Icon(Icons.check_circle, color: Colors.green, size: 20),
//                           SizedBox(width: 8),
//                           Text(
//                             'Identity Verified',
//                             style: TextStyle(color: Colors.white),
//                           ),
//                         ],
//                       ),
//                       SizedBox(height: 8),
//                       Row(
//                         children: [
//                           Icon(Icons.check_circle, color: Colors.green, size: 20),
//                           SizedBox(width: 8),
//                           Text(
//                             'Behavioral Pattern Matched',
//                             style: TextStyle(color: Colors.white),
//                           ),
//                         ],
//                       ),
//                       SizedBox(height: 8),
//                       Row(
//                         children: [
//                           Icon(Icons.check_circle, color: Colors.green, size: 20),
//                           SizedBox(width: 8),
//                           Text(
//                             'Session Secure',
//                             style: TextStyle(color: Colors.white),
//                           ),
//                         ],
//                       ),
//                     ],
//                   ),
//                 ),
                
//                 SizedBox(height: 30),
                
//                 // Quick Actions
//                 Text(
//                   'Quick Actions',
//                   style: TextStyle(
//                     fontSize: 20,
//                     fontWeight: FontWeight.bold,
//                     color: Colors.white,
//                   ),
//                 ),
                
//                 SizedBox(height: 20),
                
//                 Expanded(
//                   child: GridView.count(
//                     crossAxisCount: 2,
//                     crossAxisSpacing: 16,
//                     mainAxisSpacing: 16,
//                     children: [
//                       _buildActionCard(
//                         'Transfer Money',
//                         Icons.send,
//                         Colors.blue,
//                       ),
//                       _buildActionCard(
//                         'View Balance',
//                         Icons.account_balance_wallet,
//                         Colors.green,
//                       ),
//                       _buildActionCard(
//                         'Transaction History',
//                         Icons.history,
//                         Colors.orange,
//                       ),
//                       _buildActionCard(
//                         'Security Settings',
//                         Icons.settings_outlined,
//                         Colors.purple,
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
  
//   Widget _buildActionCard(String title, IconData icon, Color color) {
//     return Container(
//       decoration: BoxDecoration(
//         color: Colors.white.withOpacity(0.1),
//         borderRadius: BorderRadius.circular(16),
//         border: Border.all(
//           color: Colors.white.withOpacity(0.3),
//         ),
//       ),
//       child: Material(
//         color: Colors.transparent,
//         child: InkWell(
//           borderRadius: BorderRadius.circular(16),
//           onTap: () {},
//           child: Padding(
//             padding: EdgeInsets.all(20),
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 Icon(
//                   icon,
//                   size: 40,
//                   color: color,
//                 ),
//                 SizedBox(height: 12),
//                 Text(
//                   title,
//                   textAlign: TextAlign.center,
//                   style: TextStyle(
//                     fontSize: 14,
//                     fontWeight: FontWeight.w600,
//                     color: Colors.white,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }