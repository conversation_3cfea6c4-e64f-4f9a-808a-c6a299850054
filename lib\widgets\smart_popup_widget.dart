import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import '../services/challenge_generator.dart';

class SmartPopupWidget extends StatefulWidget {
  final ChallengeData challenge;
  final Function(ChallengeResult) onChallengeComplete;
  final VoidCallback? onTimeout;
  final VoidCallback? onCancel;

  const SmartPopupWidget({
    Key? key,
    required this.challenge,
    required this.onChallengeComplete,
    this.onTimeout,
    this.onCancel,
  }) : super(key: key);

  @override
  State<SmartPopupWidget> createState() => _SmartPopupWidgetState();
}

class _SmartPopupWidgetState extends State<SmartPopupWidget>
    with TickerProviderStateMixin {
  int? _selectedAnswer;
  late Timer _timeoutTimer;
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  final DateTime _startTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeTimer();
    _initializeAnimations();
  }

  void _initializeTimer() {
    _timeoutTimer = Timer(
      Duration(seconds: widget.challenge.timeoutSeconds),
      () {
        if (mounted) {
          widget.onTimeout?.call();
          _completeChallenge(isTimeout: true);
        }
      },
    );
  }

  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: Duration(seconds: widget.challenge.timeoutSeconds),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.linear,
    ));
    
    _progressController.forward();
  }

  void _completeChallenge({bool isTimeout = false}) {
    if (!mounted) return;
    
    _timeoutTimer.cancel();
    _progressController.stop();
    
    if (!isTimeout && _selectedAnswer != null) {
      final result = ChallengeResult(
        challengeId: widget.challenge.id,
        selectedAnswer: _selectedAnswer!,
        isCorrect: _isAnswerCorrect(),
        responseTime: DateTime.now().difference(_startTime),
        timestamp: DateTime.now(),
      );
      
      widget.onChallengeComplete(result);
    }
    
    Navigator.of(context).pop();
  }

  bool _isAnswerCorrect() {
    if (widget.challenge.correctAnswerIndex == null) {
      // For privacy-safe challenges, we accept any reasonable answer
      return true;
    }
    return _selectedAnswer == widget.challenge.correctAnswerIndex;
  }

  Color _getRiskColor() {
    if (widget.challenge.riskScore > 0.8) return Colors.red;
    if (widget.challenge.riskScore > 0.5) return Colors.orange;
    return Colors.blue;
  }

  String _getRiskLabel() {
    if (widget.challenge.riskScore > 0.8) return 'High Security Alert';
    if (widget.challenge.riskScore > 0.5) return 'Security Verification';
    return 'Quick Check';
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        widget.onCancel?.call();
        return true;
      },
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.security,
                  color: _getRiskColor(),
                  size: 24,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _getRiskLabel(),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: _getRiskColor(),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, child) {
                return LinearProgressIndicator(
                  value: _progressAnimation.value,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(_getRiskColor()),
                );
              },
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'To ensure account security, please answer this quick question:',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                widget.challenge.question,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            SizedBox(height: 16),
            ...widget.challenge.options.asMap().entries.map((entry) {
              final index = entry.key;
              final option = entry.value;
              
              return Container(
                margin: EdgeInsets.only(bottom: 8),
                child: InkWell(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    setState(() {
                      _selectedAnswer = index;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _selectedAnswer == index 
                          ? _getRiskColor().withOpacity(0.1)
                          : Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _selectedAnswer == index 
                            ? _getRiskColor()
                            : Colors.grey[300]!,
                        width: _selectedAnswer == index ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Radio<int>(
                          value: index,
                          groupValue: _selectedAnswer,
                          onChanged: (value) {
                            HapticFeedback.lightImpact();
                            setState(() {
                              _selectedAnswer = value;
                            });
                          },
                          activeColor: _getRiskColor(),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            option,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: _selectedAnswer == index 
                                  ? FontWeight.w500 
                                  : FontWeight.normal,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              widget.onCancel?.call();
              Navigator.of(context).pop();
            },
            child: Text(
              'Cancel',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          ElevatedButton(
            onPressed: _selectedAnswer != null 
                ? () => _completeChallenge()
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _getRiskColor(),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('Submit'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _timeoutTimer.cancel();
    _progressController.dispose();
    super.dispose();
  }
}

class ChallengeManager {
  static final ChallengeManager _instance = ChallengeManager._internal();
  factory ChallengeManager() => _instance;
  ChallengeManager._internal();

  final ChallengeGenerator _generator = ChallengeGenerator();
  final List<ChallengeResult> _challengeHistory = [];

  /// Show a smart popup challenge
  Future<ChallengeResult?> showChallenge(
    BuildContext context,
    double riskScore,
    Map<String, dynamic> behavioralContext,
  ) async {
    final challenge = _generator.generateChallenge(riskScore, behavioralContext);
    
    ChallengeResult? result;
    
    await showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => SmartPopupWidget(
        challenge: challenge,
        onChallengeComplete: (challengeResult) {
          result = challengeResult;
          _challengeHistory.add(challengeResult);
        },
        onTimeout: () {
          // Log timeout event
          debugPrint('Challenge timeout: ${challenge.id}');
        },
        onCancel: () {
          // Log cancellation
          debugPrint('Challenge cancelled: ${challenge.id}');
        },
      ),
    );
    
    return result;
  }

  /// Show multiple challenges for high-risk situations
  Future<List<ChallengeResult>> showChallengeSequence(
    BuildContext context,
    double riskScore,
    Map<String, dynamic> behavioralContext,
  ) async {
    final challenges = await _generator.generateChallengeSequence(riskScore, behavioralContext);
    final results = <ChallengeResult>[];
    
    for (final challenge in challenges) {
      final result = await showDialog<ChallengeResult>(
        context: context,
        barrierDismissible: false,
        builder: (context) => SmartPopupWidget(
          challenge: challenge,
          onChallengeComplete: (challengeResult) {
            _challengeHistory.add(challengeResult);
            Navigator.of(context).pop(challengeResult);
          },
        ),
      );
      
      if (result != null) {
        results.add(result);
      } else {
        // User cancelled or timeout, break sequence
        break;
      }
    }
    
    return results;
  }

  /// Get challenge history for analysis
  List<ChallengeResult> get challengeHistory => List.unmodifiable(_challengeHistory);

  /// Calculate challenge success rate
  double get challengeSuccessRate {
    if (_challengeHistory.isEmpty) return 1.0;
    
    final successful = _challengeHistory.where((r) => r.isCorrect).length;
    return successful / _challengeHistory.length;
  }

  /// Get average response time
  Duration get averageResponseTime {
    if (_challengeHistory.isEmpty) return Duration.zero;
    
    final totalMs = _challengeHistory
        .map((r) => r.responseTime.inMilliseconds)
        .reduce((a, b) => a + b);
    
    return Duration(milliseconds: totalMs ~/ _challengeHistory.length);
  }

  /// Clear history (for privacy)
  void clearHistory() {
    _challengeHistory.clear();
  }
}
