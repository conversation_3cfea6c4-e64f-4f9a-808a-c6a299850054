import 'package:flutter/material.dart';
import '../utils/constants.dart' as constants;
import '../utils/currency_formatter.dart';
import '../routes/app_routes.dart';

class PaymentSuccessScreen extends StatelessWidget {
  final String amount;
  final String recipient;
  final String transactionId;
  final String paymentCode;
  final String merchantName;
  final String date;
  final String time;
  final String discount;

  const PaymentSuccessScreen({
    Key? key,
    this.amount = '₹1,27,287.20',
    this.recipient = '<PERSON><PERSON><PERSON>',
    this.transactionId = '***************',
    this.paymentCode = 'PAY-001822',
    this.merchantName = 'iBox Indonesia',
    this.date = 'March 23, 2023',
    this.time = '08:30 PM',
    this.discount = '₹5,900.00',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: constants.AppColors.bankingBackground,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const Spacer(),
              
              // Success Icon
              _buildSuccessIcon(),
              const SizedBox(height: 32),
              
              // Payment Success Title
              const Text(
                'Payment Success',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 40),
              
              // Amount
              Text(
                amount,
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              
              // Discount Badge
              Container(
                margin: const EdgeInsets.only(top: 8),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: constants.AppColors.bankingPrimary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Save 30%',
                  style: TextStyle(
                    color: constants.AppColors.bankingPrimary,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Transaction Details
              _buildTransactionDetails(),
              
              const Spacer(),
              
              // Action Buttons
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSuccessIcon() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: constants.AppColors.creditGreen.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Stack(
        children: [
          // Animated circles
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: constants.AppColors.creditGreen.withOpacity(0.3),
                  width: 2,
                ),
              ),
            ),
          ),
          // Success checkmark
          const Center(
            child: Icon(
              Icons.check,
              size: 60,
              color: constants.AppColors.creditGreen,
            ),
          ),
          // Decorative elements
          Positioned(
            top: 20,
            right: 10,
            child: Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Color(0xFF8B5CF6),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Positioned(
            bottom: 15,
            left: 15,
            child: Container(
              width: 6,
              height: 6,
              decoration: const BoxDecoration(
                color: Color(0xFFF59E0B),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Positioned(
            top: 35,
            left: 5,
            child: Container(
              width: 4,
              height: 4,
              decoration: const BoxDecoration(
                color: Color(0xFFEF4444),
                shape: BoxShape.circle,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionDetails() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: constants.AppColors.bankingSurface,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          _buildDetailRow('No. References', transactionId),
          _buildDetailRow('Payment Code', paymentCode),
          _buildDetailRow('Merchant Name', merchantName),
          _buildDetailRow('Date', date),
          _buildDetailRow('Time', time),
          _buildDetailRow('Sender', recipient),
          _buildDetailRow('Discount', discount, isLast: true),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isLast = false}) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLast ? 0 : 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // Detail Payment Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () {
              // Show detailed payment information
            },
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: constants.AppColors.bankingPrimary),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'Detail Payment',
              style: TextStyle(
                color: constants.AppColors.bankingPrimary,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // Back Home Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              Navigator.pushNamedAndRemoveUntil(
                context,
                AppRoutes.dashboard,
                (route) => false,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: constants.AppColors.bankingPrimary,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: const Text(
              'Back Home',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
