// import 'package:geolocator/geolocator.dart';  // Temporarily disabled

class GeoFenceService {
  static const double _authorizedLatitude = 28.6139;
  static const double _authorizedLongitude = 77.2090;
  static const double _radiusInMeters = 900000;

  static Future<bool> isUserWithinBoundary({bool requestIfNeeded = false}) async {
    // Mock implementation for testing - always return true
    // In a real app, this would check actual location against boundary
    return true;
  }
}
