import 'dart:ui';

class DataCollector {
  List<int> keystrokeTimes = [];
  List<double> tapPressures = [];
  List<Offset> tapPositions = [];
  List<double> swipeVelocities = [];
  int sessionStartTime = DateTime.now().millisecondsSinceEpoch;

  void addKeystroke(int timestamp) {
    keystrokeTimes.add(timestamp);
  }

  void addTapData(double pressure, Offset position) {
    tapPressures.add(pressure);
    tapPositions.add(position);
  }

  void addSwipeVelocity(double velocity) {
    swipeVelocities.add(velocity);
  }

  Map<String, dynamic> toJson() {
    return {
      'keystroke_intervals': _calculateIntervals(keystrokeTimes),
      'avg_tap_pressure': tapPressures.isEmpty
          ? 0
          : tapPressures.reduce((a, b) => a + b) / tapPressures.length,
      'tap_positions':
          tapPositions.map((pos) => {'x': pos.dx, 'y': pos.dy}).toList(),
      'avg_swipe_velocity': swipeVelocities.isEmpty
          ? 0
          : swipeVelocities.reduce((a, b) => a + b) / swipeVelocities.length,
      'session_duration':
          DateTime.now().millisecondsSinceEpoch - sessionStartTime,
    };
  }

  List<int> _calculateIntervals(List<int> times) {
    if (times.length < 2) return [];
    List<int> intervals = [];
    for (int i = 1; i < times.length; i++) {
      intervals.add(times[i] - times[i - 1]);
    }
    return intervals;
  }

  String getCurrentUsageTimeOfDay() {
    final hour = DateTime.now().hour;
    if (hour >= 5 && hour < 12) return 'Morning';
    if (hour >= 12 && hour < 17) return 'Afternoon';
    if (hour >= 17 && hour < 21) return 'Evening';
    return 'Night';
  }

  /// Get data statistics for privacy dashboard
  Map<String, dynamic> getDataStatistics() {
    return {
      'totalKeystrokes': keystrokeTimes.length,
      'totalTaps': tapPressures.length,
      'totalSwipes': swipeVelocities.length,
      'sessionDuration': DateTime.now().millisecondsSinceEpoch - sessionStartTime,
      'averageTapPressure': tapPressures.isEmpty
          ? 0
          : tapPressures.reduce((a, b) => a + b) / tapPressures.length,
      'averageSwipeVelocity': swipeVelocities.isEmpty
          ? 0
          : swipeVelocities.reduce((a, b) => a + b) / swipeVelocities.length,
      'dataPoints': keystrokeTimes.length + tapPressures.length + swipeVelocities.length,
    };
  }

  /// Export all collected data
  Future<Map<String, dynamic>> exportAllData() async {
    return {
      'exportDate': DateTime.now().toIso8601String(),
      'sessionStartTime': sessionStartTime,
      'keystrokeTimes': keystrokeTimes,
      'tapPressures': tapPressures,
      'tapPositions': tapPositions.map((pos) => {'x': pos.dx, 'y': pos.dy}).toList(),
      'swipeVelocities': swipeVelocities,
      'statistics': getDataStatistics(),
      'usageTimeOfDay': getCurrentUsageTimeOfDay(),
    };
  }

  /// Delete old data (older than specified days)
  Future<void> deleteOldData(int days) async {
    final cutoffTime = DateTime.now().subtract(Duration(days: days)).millisecondsSinceEpoch;
    
    // In a real implementation, this would filter based on timestamps
    // For now, we'll just clear data older than the session start time
    if (sessionStartTime < cutoffTime) {
      keystrokeTimes.clear();
      tapPressures.clear();
      tapPositions.clear();
      swipeVelocities.clear();
    }
  }

  /// Delete all collected data
  Future<void> deleteAllData() async {
    keystrokeTimes.clear();
    tapPressures.clear();
    tapPositions.clear();
    swipeVelocities.clear();
    sessionStartTime = DateTime.now().millisecondsSinceEpoch;
  }
}
