import 'package:flutter/material.dart';

class AppColors {
  static const Color gradientStart = Color(0xFF667eea);
  static const Color gradientMid = Color(0xFF764ba2);
  static const Color gradientEnd = Color(0xFFf093fb);

  static const Color buttonStart = Color(0xFF4facfe);
  static const Color buttonEnd = Color(0xFF00f2fe);

  static const Color white = Colors.white;
  static const Color white70 = Colors.white70;
  static const Color white80 = Color.fromRGBO(255, 255, 255, 0.8);
  static const Color white30 = Color.fromRGBO(255, 255, 255, 0.3);

  // Legacy colors for compatibility
  static const Color primary = Color(0xFF4285F4);
  static const Color background = Color(0xFF0D1B2A);
  static const Color surface = Color(0xFF1B263B);
  static const Color error = Color(0xFFEF4444);

  // Banking specific colors
  static const Color bankingPrimary = Color(0xFF4285F4);
  static const Color bankingSecondary = Color(0xFF34A853);
  static const Color bankingAccent = Color(0xFFFF6B35);
  static const Color bankingBackground = Color(0xFF0D1B2A);
  static const Color bankingSurface = Color(0xFF1B263B);
  static const Color bankingCard = Color(0xFF2A3441);

  // Transaction colors
  static const Color creditGreen = Color(0xFF10B981);
  static const Color debitRed = Color(0xFFEF4444);
  static const Color pendingOrange = Color(0xFFF59E0B);
}

class BankingConstants {
  static const String appName = 'SecureBank';
  static const String tagline = 'Easy Banking With the Simplest Way';
  static const String currency = '₹';
  static const String defaultAccountNumber = '2004 2001 0611 7177';
  static const String defaultUserName = 'Dhimas Mohammed';

  // Demo data
  static const double defaultBalance = 1024.00;
  static const String defaultCardNumber = '2004 2001 0611 7177';
  static const String defaultBankName = 'Artho Bank';
}

class AppSpacing {
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;
}

class AppTextStyles {
  static const TextStyle title = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: Colors.white,
    letterSpacing: 1.5,
  );

  static const TextStyle subtitle = TextStyle(
    fontSize: 16,
    color: AppColors.white80,
    letterSpacing: 0.5,
  );

  static const TextStyle hint = TextStyle(
    color: AppColors.white70,
  );

  static const TextStyle input = TextStyle(
    color: AppColors.white,
  );

  static const TextStyle progress = TextStyle(
    fontSize: 14,
    color: AppColors.white80,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    color: Colors.white,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    color: Colors.white,
  );
}
