import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class NoPasteTextField extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final String hint;
  final IconData icon;
  final bool obscure;
  final Function(String)? onChanged;

  const NoPasteTextField({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.hint,
    required this.icon,
    this.obscure = false,
    this.onChanged,
  });

  @override
  State<NoPasteTextField> createState() => _NoPasteTextFieldState();
}

class _NoPasteTextFieldState extends State<NoPasteTextField> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller;
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      focusNode: widget.focusNode,
      obscureText: widget.obscure,
      enableInteractiveSelection: false, // disables long press menu
      enableSuggestions: false,
      autocorrect: false,
      keyboardType: widget.obscure ? TextInputType.visiblePassword : TextInputType.text,
      onChanged: widget.onChanged,
      style: TextStyle(color: Colors.white),
      decoration: InputDecoration(
        hintText: widget.hint,
        hintStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
        prefixIcon: Icon(widget.icon, color: Colors.white.withOpacity(0.8)),
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      ),
      toolbarOptions: const ToolbarOptions(copy: false, cut: false, paste: false, selectAll: false),
      inputFormatters: [
        _NoPasteInputFormatter(), // <- paste-blocker
      ],
    );
  }
}

/// Formatter that blocks multi-character insertions (likely paste/autofill).
class _NoPasteInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    // If more than 1 character added at once, block it
    final oldText = oldValue.text;
    final newText = newValue.text;

    final isPasting = (newText.length - oldText.length) > 1 ||
        newValue.text != (oldValue.text + newValue.text.replaceAll(oldValue.text, ''));

    if (isPasting) {
      // Reject paste
      return oldValue;
    }

    return newValue;
  }
}
