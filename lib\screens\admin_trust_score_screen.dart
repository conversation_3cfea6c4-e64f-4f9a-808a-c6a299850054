import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/anomaly_detector.dart';
import '../models/behavioral_data_model.dart';

class AdminTrustScoreScreen extends StatefulWidget {
  final String adminId;

  const AdminTrustScoreScreen({required this.adminId, Key? key}) : super(key: key);

  @override
  State<AdminTrustScoreScreen> createState() => _AdminTrustScoreScreenState();
}

class _AdminTrustScoreScreenState extends State<AdminTrustScoreScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AnomalyDetector _anomalyDetector = AnomalyDetector();
  
  List<TrustScoreData> _trustScores = [];
  List<String> _userIds = [];
  String _selectedUserId = '';
  bool _isLoading = true;
  String _selectedTimeRange = '7d';

  @override
  void initState() {
    super.initState();
    _loadUserList();
  }

  Future<void> _loadUserList() async {
    try {
      final users = await _firestore.collection('users').get();
      setState(() {
        _userIds = users.docs.map((doc) => doc.id).toList();
        if (_userIds.isNotEmpty) {
          _selectedUserId = _userIds.first;
          _loadTrustScoreData();
        }
      });
    } catch (e) {
      setState(() => _isLoading = false);
      print('Error loading users: $e');
    }
  }

  Future<void> _loadTrustScoreData() async {
    if (_selectedUserId.isEmpty) return;
    
    setState(() => _isLoading = true);
    
    try {
      final DateTime endDate = DateTime.now();
      final DateTime startDate = _getStartDate(endDate);

      final sessions = await _firestore
          .collection('users')
          .doc(_selectedUserId)
          .collection('sessions')
          .where('timestamp', isGreaterThanOrEqualTo: startDate.toIso8601String())
          .where('timestamp', isLessThanOrEqualTo: endDate.toIso8601String())
          .orderBy('timestamp')
          .get();

      final List<TrustScoreData> scores = [];
      
      for (var doc in sessions.docs) {
        final data = doc.data();
        final behavioralData = BehavioralData();
        
        // Reconstruct behavioral data from Firestore
        if (data['keystroke_intervals'] != null) {
          behavioralData.keystrokeTimes = List<int>.from(data['keystroke_intervals'] ?? []);
        }
        if (data['avg_tap_pressure'] != null) {
          behavioralData.tapPressures = [data['avg_tap_pressure'].toDouble()];
        }
        if (data['session_duration'] != null) {
          behavioralData.sessionStartTime = 
              DateTime.parse(data['timestamp']).millisecondsSinceEpoch - (data['session_duration'] as num).toInt();
        }

        final trustScore = _anomalyDetector.calculateBehavioralScore(behavioralData);
        final timestamp = DateTime.parse(data['timestamp']);
        
        scores.add(TrustScoreData(
          timestamp: timestamp,
          trustScore: trustScore,
          sessionId: doc.id,
          sessionDuration: data['session_duration'] ?? 0,
          keystrokeCount: (data['keystroke_intervals'] as List?)?.length ?? 0,
          tapCount: (data['tap_positions'] as List?)?.length ?? 0,
        ));
      }

      setState(() {
        _trustScores = scores;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      print('Error loading trust score data: $e');
    }
  }

  DateTime _getStartDate(DateTime endDate) {
    switch (_selectedTimeRange) {
      case '24h':
        return endDate.subtract(Duration(hours: 24));
      case '7d':
        return endDate.subtract(Duration(days: 7));
      case '30d':
        return endDate.subtract(Duration(days: 30));
      case '90d':
        return endDate.subtract(Duration(days: 90));
      default:
        return endDate.subtract(Duration(days: 7));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFF1a1a2e),
      appBar: AppBar(
        title: Text('Trust Score Analytics', style: TextStyle(color: Colors.white)),
        backgroundColor: Color(0xFF16213e),
        iconTheme: IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF1a1a2e), Color(0xFF16213e), Color(0xFF0f3460)],
          ),
        ),
        child: _isLoading
            ? Center(child: CircularProgressIndicator(color: Colors.white))
            : Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildControlPanel(),
                    SizedBox(height: 20),
                    _buildStatsCards(),
                    SizedBox(height: 20),
                    Expanded(child: _buildTrustScoreChart()),
                    SizedBox(height: 20),
                    _buildSessionList(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildControlPanel() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('User', style: TextStyle(color: Colors.white70, fontSize: 12)),
                    DropdownButton<String>(
                      value: _selectedUserId.isEmpty ? null : _selectedUserId,
                      dropdownColor: Color(0xFF16213e),
                      style: TextStyle(color: Colors.white),
                      items: _userIds.map((userId) {
                        return DropdownMenuItem(
                          value: userId,
                          child: Text(userId.length > 20 ? '${userId.substring(0, 20)}...' : userId),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() => _selectedUserId = value);
                          _loadTrustScoreData();
                        }
                      },
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Time Range', style: TextStyle(color: Colors.white70, fontSize: 12)),
                    DropdownButton<String>(
                      value: _selectedTimeRange,
                      dropdownColor: Color(0xFF16213e),
                      style: TextStyle(color: Colors.white),
                      items: [
                        DropdownMenuItem(value: '24h', child: Text('Last 24 Hours')),
                        DropdownMenuItem(value: '7d', child: Text('Last 7 Days')),
                        DropdownMenuItem(value: '30d', child: Text('Last 30 Days')),
                        DropdownMenuItem(value: '90d', child: Text('Last 90 Days')),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() => _selectedTimeRange = value);
                          _loadTrustScoreData();
                        }
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCards() {
    if (_trustScores.isEmpty) return SizedBox();

    final avgTrustScore = _trustScores.map((e) => e.trustScore).reduce((a, b) => a + b) / _trustScores.length;
    final maxTrustScore = _trustScores.map((e) => e.trustScore).reduce((a, b) => a > b ? a : b);
    final minTrustScore = _trustScores.map((e) => e.trustScore).reduce((a, b) => a < b ? a : b);
    final totalSessions = _trustScores.length;

    return Row(
      children: [
        _buildStatCard('Avg Trust Score', '${(avgTrustScore * 100).toInt()}%', Icons.analytics, Colors.blue),
        SizedBox(width: 12),
        _buildStatCard('Max Score', '${(maxTrustScore * 100).toInt()}%', Icons.trending_up, Colors.green),
        SizedBox(width: 12),
        _buildStatCard('Min Score', '${(minTrustScore * 100).toInt()}%', Icons.trending_down, Colors.orange),
        SizedBox(width: 12),
        _buildStatCard('Sessions', '$totalSessions', Icons.analytics, Colors.purple),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 20),
            SizedBox(height: 4),
            Text(value, style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold)),
            Text(title, style: TextStyle(color: Colors.white70, fontSize: 10)),
          ],
        ),
      ),
    );
  }

  Widget _buildTrustScoreChart() {
    if (_trustScores.isEmpty) {
      return Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Text('No data available', style: TextStyle(color: Colors.white70)),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Trust Score Trend', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
          SizedBox(height: 16),
          Expanded(
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: true,
                  getDrawingHorizontalLine: (value) => FlLine(color: Colors.white.withOpacity(0.1), strokeWidth: 1),
                  getDrawingVerticalLine: (value) => FlLine(color: Colors.white.withOpacity(0.1), strokeWidth: 1),
                ),
                titlesData: FlTitlesData(
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        if (value.toInt() < _trustScores.length) {
                          final data = _trustScores[value.toInt()];
                          return Text(
                            '${data.timestamp.day}/${data.timestamp.month}',
                            style: TextStyle(color: Colors.white70, fontSize: 10),
                          );
                        }
                        return Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          '${(value * 100).toInt()}%',
                          style: TextStyle(color: Colors.white70, fontSize: 10),
                        );
                      },
                    ),
                  ),
                  topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(color: Colors.white.withOpacity(0.2)),
                ),
                minX: 0,
                maxX: _trustScores.length.toDouble() - 1,
                minY: 0,
                maxY: 1,
                lineBarsData: [
                  LineChartBarData(
                    spots: _trustScores.asMap().entries.map((entry) {
                      return FlSpot(entry.key.toDouble(), entry.value.trustScore);
                    }).toList(),
                    isCurved: true,
                    color: Colors.cyan,
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 4,
                          color: Colors.cyan,
                          strokeWidth: 2,
                          strokeColor: Colors.white,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      color: Colors.cyan.withOpacity(0.1),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionList() {
    if (_trustScores.isEmpty) return SizedBox();

    return Container(
      height: 200,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Recent Sessions', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
          SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              itemCount: _trustScores.length,
              itemBuilder: (context, index) {
                final score = _trustScores[index];
                return Container(
                  margin: EdgeInsets.only(bottom: 8),
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: _getScoreColor(score.trustScore).withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _getScoreColor(score.trustScore),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${score.timestamp.day}/${score.timestamp.month} ${score.timestamp.hour}:${score.timestamp.minute.toString().padLeft(2, '0')}',
                              style: TextStyle(color: Colors.white, fontSize: 12),
                            ),
                            Text(
                              'Score: ${(score.trustScore * 100).toInt()}% | Duration: ${(score.sessionDuration / 1000).toInt()}s',
                              style: TextStyle(color: Colors.white70, fontSize: 10),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 0.8) return Colors.green;
    if (score >= 0.6) return Colors.orange;
    return Colors.red;
  }
}

class TrustScoreData {
  final DateTime timestamp;
  final double trustScore;
  final String sessionId;
  final int sessionDuration;
  final int keystrokeCount;
  final int tapCount;

  TrustScoreData({
    required this.timestamp,
    required this.trustScore,
    required this.sessionId,
    required this.sessionDuration,
    required this.keystrokeCount,
    required this.tapCount,
  });
}
